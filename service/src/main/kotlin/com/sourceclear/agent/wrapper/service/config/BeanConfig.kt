package com.sourceclear.agent.wrapper.service.config

import com.fasterxml.jackson.annotation.JsonInclude
import io.awspring.cloud.sqs.config.SqsMessageListenerContainerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.converter.json.Jackson2ObjectMapperBuilder
import org.springframework.messaging.converter.MappingJackson2MessageConverter
import org.springframework.messaging.converter.MessageConverter
import software.amazon.awssdk.services.sqs.SqsAsyncClient


@Configuration
class BeanConfig {
  @Bean
  fun messageConverter(): MessageConverter {
    val builder = Jackson2ObjectMapperBuilder()
    builder.serializationInclusion(JsonInclude.Include.NON_EMPTY)

    val mappingJackson2MessageConverter = MappingJackson2MessageConverter()

    mappingJackson2MessageConverter.objectMapper = builder.build()

    return mappingJackson2MessageConverter
  }

  @Bean
  fun sqsMessageListenerContainerFactory(@Qualifier("sqsAsyncClient") sqsAsyncClient: SqsAsyncClient): SqsMessageListenerContainerFactory<Any> {
    return SqsMessageListenerContainerFactory
            .builder<Any>()
            .sqsAsyncClient(sqsAsyncClient)
            .build()
  }
}

