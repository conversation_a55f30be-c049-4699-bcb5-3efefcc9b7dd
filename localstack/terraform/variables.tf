
variable "environment" {
    type = string
    description = "The environment like in QA, prod, stage, etc"
    default = "localstack"
}

variable "evidence_sqs_name" {
    type = string
    description = "Name of the SQS. Should match spring boot application profile's sqs expectation."
    default = "jsyeo-srcclr-evidence"
}

variable "region" {
    default = "us-east-1"
}

variable "scan_request_sqs_name" {
    type = string
    description = "Name of the SQS. Should match spring boot application profile's sqs expectation."
    default = "jsyeo-scan-request"
}

variable "s3_bucket" {
    type = string
    description = "Name of the s3 bucket name. Should match spring boot application profile's s3 bucket name expectation."
    default = "jyeo-test"
}
