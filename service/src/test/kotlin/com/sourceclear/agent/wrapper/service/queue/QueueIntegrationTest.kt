package com.sourceclear.agent.wrapper.service.queue

import com.fasterxml.jackson.databind.ObjectMapper
import com.github.kittinunf.result.Result
import com.sourceclear.agent.wrapper.scanconfig.client.Client
import com.sourceclear.agent.wrapper.service.config.AwsConfig
import com.sourceclear.agent.wrapper.service.config.BeanConfig
import com.sourceclear.agent.wrapper.service.config.SourceClearCommunication
import com.sourceclear.agent.wrapper.service.config.SourceClearConfig
import com.sourceclear.agent.wrapper.service.objects.ScanRequestMessage
import com.sourceclear.agent.wrapper.service.utils.CloseableTempIO
import com.sourceclear.agent.wrapper.service.utils.aesCipherWith
import io.awspring.cloud.s3.InMemoryBufferingS3OutputStreamProvider
import io.awspring.cloud.s3.Jackson2JsonS3ObjectConverter
import io.awspring.cloud.s3.S3Template
import io.awspring.cloud.sqs.operations.SqsSendOptions
import io.awspring.cloud.sqs.operations.SqsTemplate
import io.awspring.cloud.test.sqs.SqsTest
import org.apache.commons.codec.binary.Hex
import org.awaitility.Awaitility.await
import org.awaitility.kotlin.matches
import org.awaitility.kotlin.untilCallTo
import org.json.simple.JSONObject
import org.json.simple.parser.JSONParser
import org.junit.jupiter.api.BeforeAll
import org.junit.jupiter.api.Disabled
import org.junit.jupiter.api.Test
import org.mockito.Mockito
import org.mockito.kotlin.any
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Import
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ActiveProfiles
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.testcontainers.containers.Container.ExecResult
import org.testcontainers.containers.localstack.LocalStackContainer
import org.testcontainers.junit.jupiter.Container
import org.testcontainers.junit.jupiter.Testcontainers
import org.testcontainers.utility.DockerImageName
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.presigner.S3Presigner
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import java.io.BufferedInputStream
import java.io.FileInputStream
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.time.Duration
import java.util.*
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.crypto.Cipher
import javax.crypto.CipherInputStream
import kotlin.test.fail
import kotlin.test.assertTrue


@Disabled("Unstable to be ran in gitlab pipeline. For now, best ran on local machine. See https://veracode.atlassian.net/browse/SCA-15956")
@Testcontainers
@SqsTest(QueueHandler::class)
@ActiveProfiles("localstack")
@Import(
    value = [QueueIntegrationTest.QueueIntegrationTestConfig::class, AwsConfig::class, SourceClearConfig::class,
        QueueHandler::class, BeanConfig::class]
)
class QueueIntegrationTest {

    companion object {
        @Container
        public val localStack: LocalStackContainer =
            LocalStackContainer(DockerImageName.parse("localstack/localstack:3.0.0"))
                .withServices(LocalStackContainer.Service.S3, LocalStackContainer.Service.SQS)

        private val BUCKET_NAME = "jyeo-test"
        private val KEY = "27e08ca1edec290fccd8e69b29b7b5784c9de82924dfac2bb3bbfdcddb5e0843"
        private val IV = "f7d7a723a479d0ae32454eff472a0282"
        private val CIPHER = with(Hex()) {
            aesCipherWith(
                decode(ByteBuffer.wrap(KEY.toByteArray())),
                decode(ByteBuffer.wrap(IV.toByteArray())),
                Cipher.ENCRYPT_MODE
            )
        }
        private val SCAN_REQUEST_QUEUE = "jsyeo-scan-request"
        private var SCAN_REQUEST_QUEUE_URL = ""
        private val SCAN_RESULT_QUEUE = "jsyeo-srcclr-evidence"
        private var SCAN_RESULT_QUEUE_URL = ""

        @JvmStatic
        @BeforeAll
        fun init(): Unit {
            val jsonParser = JSONParser()

            //create msg queue from SCS, the scan request queue.
            var execResult =
                localStack.execInContainer("awslocal", "sqs", "create-queue", "--queue-name", SCAN_REQUEST_QUEUE)
            verifySuccessfulExecResultReturnCode(execResult, "failed to create the scan request queue")
            SCAN_REQUEST_QUEUE_URL = (jsonParser.parse(execResult.stdout) as JSONObject).get("QueueUrl") as String

            //create msg queue to platform, the list of evidences from the scan
            execResult =
                localStack.execInContainer("awslocal", "sqs", "create-queue", "--queue-name", SCAN_RESULT_QUEUE)
            verifySuccessfulExecResultReturnCode(
                execResult,
                "failed to create the scan result queue to publish evidence"
            )
            SCAN_RESULT_QUEUE_URL = (jsonParser.parse(execResult.stdout) as JSONObject).get("QueueUrl") as String

            //create s3 bucket for evidence
            execResult = localStack.execInContainer("awslocal", "s3api", "create-bucket", "--bucket", BUCKET_NAME)
            verifySuccessfulExecResultReturnCode(execResult, "failed to create the s3 bucket for large evidence msg")

            //
            // Need to override properties with port(s) since testcontainers will choose one 'arbitrary' port to bind automatically.
            //
            val localStackGatewayListenerPort = localStack.getMappedPort(4566)
            System.setProperty("sqs.queue", "http://localhost:$localStackGatewayListenerPort/000000000000/jsyeo-scan-request")
            System.setProperty("spring.cloud.aws.endpoint", "http://localhost:$localStackGatewayListenerPort")
            System.setProperty("spring.cloud.aws.s3.endpoint", "http://s3.localhost.localstack.cloud:$localStackGatewayListenerPort")
            System.setProperty("spring.cloud.aws.sqs.endpoint", "http://localhost:$localStackGatewayListenerPort")
            System.setProperty("spring.cloud.aws.kms.endpoint", "http://localhost:$localStackGatewayListenerPort")
            System.setProperty("aws.region", "us-east-1")
        }

        fun verifySuccessfulExecResultReturnCode(execResult: ExecResult, failedMsg: String) {
            if (execResult.exitCode != 0) {
                fail(failedMsg)
            }
        }
    }

    @Autowired
    private lateinit var sqsTemplate: SqsTemplate

    @Autowired
    private lateinit var s3Template: S3Template

    @Configuration
    class QueueIntegrationTestConfig {

        @Bean
        @Qualifier("localstackS3Client")
        fun localstackS3Client(): S3Client {
            return S3Client.builder()
                .endpointOverride(localStack.getEndpointOverride(LocalStackContainer.Service.S3))
                .credentialsProvider(
                    StaticCredentialsProvider.create(
                        AwsBasicCredentials.create(
                            localStack.accessKey,
                            localStack.secretKey
                        )
                    )
                )
                .region(Region.of(localStack.region))
                .build()
        }

      @Bean
      @Qualifier("localstackSqsAsyncClient")
      fun localstackSqsAsyncClient(): SqsAsyncClient {
        return SqsAsyncClient.builder()
          .endpointOverride(localStack.getEndpointOverride(LocalStackContainer.Service.SQS))
          .credentialsProvider(
            StaticCredentialsProvider.create(
              AwsBasicCredentials.create(
                localStack.accessKey,
                localStack.secretKey
              )
            )
          )
          .region(Region.of(localStack.region))
          .build()
      }

        @Bean
        fun sqsTemplate(@Qualifier("localstackSqsAsyncClient") sqsAsyncClient: SqsAsyncClient): SqsTemplate {
            return SqsTemplate.builder().sqsAsyncClient(sqsAsyncClient).build()
        }

        @Bean
        fun s3Template(@Qualifier("localstackS3Client") s3Client: S3Client): S3Template {
            return S3Template(
                s3Client, InMemoryBufferingS3OutputStreamProvider(s3Client, null),
                Jackson2JsonS3ObjectConverter(ObjectMapper()),
                S3Presigner.create()
            )
        }

      @Bean
      fun scanconfigClient(): Client {
        return Mockito.mock(Client::class.java)
      }


        @Primary
        @Bean
        fun sourceClearCommunication(): SourceClearCommunication {
          val sourceClearCommunication = Mockito.mock(SourceClearCommunication::class.java)
          Mockito.`when`(sourceClearCommunication.veracodeScan(any())).thenReturn(Result.success("Mocked: Message put"))
          return sourceClearCommunication
        }
    }

    @Test
    fun `should continue scanning after scanning unsupported zip file`() {
        val jsonParser = JSONParser()

        val testFile = "unsupportedCompressFilesInLZFSE.zip"
        val scanRequestMessage = simulateBinaries(
            setOf(
                Paths.get(
                    QueueHandlerIntegrationTest::class.java.classLoader.getResource(testFile)!!
                        .toURI()
                )
            ), binariesName = "$testFile.aes", isArchive = false
        )

        //Send a lot of scan request msg
        for(i in 1..51) {
          sqsTemplate.send { sqsSendOptions: SqsSendOptions<ScanRequestMessage> ->
            sqsSendOptions.queue(SCAN_REQUEST_QUEUE)
            sqsSendOptions.payload(scanRequestMessage)
          }
        }

        await()
            .atMost(Duration.ofMinutes(1L))
            .untilCallTo {
                val execResult = localStack.execInContainer(
                    "awslocal",
                    "sqs",
                    "get-queue-attributes",
                    "--queue-url",
                    SCAN_REQUEST_QUEUE_URL,
                    "--attribute-names",
                    "ApproximateNumberOfMessages"
                )
                ((jsonParser.parse(execResult.stdout) as JSONObject).get("Attributes") as JSONObject).get("ApproximateNumberOfMessages")
            }.matches { count -> (count!! as String).compareTo("0") == 0 }
    }

    /* Utility Functions */

    private fun simulateBinaries(
        files: Set<Path>,
        binariesName: String = "binaries",
        isArchive: Boolean = true
    ): ScanRequestMessage {
        val keyName = if (isArchive) {
            archiveUpload(binariesName, files)
        } else {
            nonArchiveUpload(binariesName, files)
        }
        return ScanRequestMessage(
            binaryPath = keyName,
            bucketName = BUCKET_NAME,
            encryptIv = IV,
            encryptKey = KEY,
            encryptedKey = "something",
            // don't bother with the following
            accountId = 1L,
            analysisId = 1L,
            analysisUnitId = 1L,
            appId = 1L,
            appName = "someAppName",
            appVerId = 1L,
            env = "something",
            jobId = 1L,
            sandboxId = 2L,
            sandbox = false,
            scanId = UUID.randomUUID().toString(),
            scaEnabled = true,
            scanConfigId = "something",
            teamIds = Collections.singletonList("1001".toLong()),
            policyLegacyId = 1L,
            policyVersion = 0,
            scanName = "test",
            submittedBy = 123,
            submitterName = "test-user"
        )
    }

    private fun archiveUpload(binariesName: String, files: Set<Path>): String {
        // TODO: make the structure more complex here
        val filename = "$binariesName.zip.aes"
        val keyName = generateBinaryPath(filename)
        CloseableTempIO.createTempDirectory("test").use { tempDir ->
            val binariesPath = tempDir.resolve("$binariesName.zip")
            ZipOutputStream(FileOutputStream(binariesPath.toFile())).use { zipOutputStream ->
                zipOutputStream.putNextEntry(ZipEntry("binaries/"))
                files.forEach {
                    zipOutputStream.putNextEntry(ZipEntry("binaries/%s".format(it.fileName)))
                    Files.copy(it, zipOutputStream)
                    zipOutputStream.closeEntry()
                }
            }
            val cipherInputStream = CipherInputStream(binariesPath.toFile().inputStream(), CIPHER)
            val binariesAesPath = tempDir.resolve(filename)
            Files.copy(cipherInputStream, binariesAesPath)
            val fileInputStream = FileInputStream(binariesAesPath.toAbsolutePath().toString())
            val bufferedInputStream = BufferedInputStream(fileInputStream)
            val s3Resource = s3Template.upload(BUCKET_NAME, keyName, bufferedInputStream)
            assertTrue(s3Resource.exists(), String.format("Failed to upload object, %s, to s3 bucket", keyName))
        }
        return keyName
    }

    private fun nonArchiveUpload(filename: String, files: Set<Path>): String {
        // TODO: make the structure more complex here
        val keyName = generateBinaryPath(filename)
        CloseableTempIO.createTempDirectory("test").use { tempDir ->
            files.forEach {
                val cipherInputStream = CipherInputStream(it.toFile().inputStream(), CIPHER)
                val binariesAesPath = tempDir.resolve(it.fileName.toString() + ".aes")
                Files.copy(cipherInputStream, binariesAesPath)

                //Upload files to s3
                val fileInputStream = FileInputStream(binariesAesPath.toAbsolutePath().toString())
                val bufferedInputStream = BufferedInputStream(fileInputStream)
                val s3Resource = s3Template.upload(BUCKET_NAME, keyName, bufferedInputStream)
                assertTrue(s3Resource.exists(), String.format("Failed to upload object, %s, to s3 bucket", keyName))
            }
        }
        return keyName
    }

    private fun generateBinaryPath(name: String): String =
        "/%s/%s/%s".format(
            UUID.randomUUID().toString().replace("-", "_"),
            UUID.randomUUID().toString().replace("-", "_"),
            name
        )

}