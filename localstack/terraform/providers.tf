terraform {
  required_version = "~>1.3.9"

  required_providers {
    aws = {
      version = "~>4.0"
    }
  }
}

provider "aws" {
  region = var.region
  access_key                  = "fake"
  secret_key                  = "fake"

  # only required for non virtual hosted-style endpoint use case.
  # https://registry.terraform.io/providers/hashicorp/aws/latest/docs#s3_use_path_style
  s3_use_path_style           = true
  skip_credentials_validation = true
  skip_metadata_api_check     = true
  skip_requesting_account_id  = true

  endpoints {
    s3  = "http://s3.localhost.localstack.cloud:4566"
    sqs = "http://localhost:4566"
  }

  default_tags {
    tags = {
      cost_center = "SCA"
      environment = var.environment
      owner = "SCA"
      product = "SCA"
      terraform_managed = true
    }
  }
}
