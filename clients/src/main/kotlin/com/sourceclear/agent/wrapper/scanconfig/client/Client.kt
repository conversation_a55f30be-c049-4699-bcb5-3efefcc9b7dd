package com.sourceclear.agent.wrapper.scanconfig.client

import com.fasterxml.jackson.databind.ObjectMapper
import com.fasterxml.jackson.databind.PropertyNamingStrategy
import com.github.kittinunf.result.Result
import com.sourceclear.agent.wrapper.http.HttpError
import com.sourceclear.agent.wrapper.http.putJson
import com.sourceclear.agent.wrapper.http.send
import org.slf4j.LoggerFactory
import java.io.File
import java.lang.Thread.sleep
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.security.KeyStore
import javax.net.ssl.KeyManagerFactory
import javax.net.ssl.SSLContext
import javax.net.ssl.TrustManagerFactory

enum class ScanStatus {
  FAILED, // When an agent scan has failed
}

data class ScanStatusRequest(val scanConfigId: String,
                             val scanId: String,
                             val status: ScanStatus,
                             val statusInfo: String?)

data class TwoWayTLSPair(val truststore: KeyStore,
                         val keystore: KeyStore,
                         val keystorePassword: CharArray)

class Client(baseUrl: String, port: Int = 80, twoWayTLSPair: TwoWayTLSPair) {
  private val baseUrl = "${baseUrl.trimEnd('/')}:$port"

  private val mapper = ObjectMapper()

  /**
   * Custom ssl context
   */
  private val sslContext: SSLContext = SSLContext.getInstance("SSL").also{
    // Configure httpclient with truststore (server's public cert)
    val trustFactory = TrustManagerFactory.getInstance(TrustManagerFactory.getDefaultAlgorithm())
    trustFactory.init(twoWayTLSPair.truststore)

    // Configure httpclient with keystore (client's private key)
    val keyFactory = KeyManagerFactory.getInstance(KeyManagerFactory.getDefaultAlgorithm())
    keyFactory.init(twoWayTLSPair.keystore, twoWayTLSPair.keystorePassword)

    it.init(keyFactory.keyManagers, trustFactory.trustManagers, null)
  }

  private val httpClient = HttpClient.newBuilder().sslContext(sslContext).build()

  /**
   * Sends the ScanStatusRequest object to the $baseURl/sca/scanconfig/scans/{scanId}/status
   */
  fun sendScanStatus(scanStatusRequest: ScanStatusRequest, retries: Int = 3): Result<String, HttpError> {
    val url = "$baseUrl/sca/scanconfig/scans/${scanStatusRequest.scanId}/status"
    val body = mapper.writeValueAsString(scanStatusRequest)

    val request = HttpRequest.newBuilder()
        .putJson(body)
        .uri(URI(url))
        .header(X_AUTH_TOKEN, dummyToken)
        .build()

    LOGGER.debug("Sending payload to scan config service:")
    LOGGER.debug(body)

    return when (val result = httpClient.send(request)) {
      is Result.Success -> {
        result
      }
      is Result.Failure -> {
        if (retries != 0) {
          sleep(5000)
          sendScanStatus(scanStatusRequest, retries - 1)
        } else {
          result
        }
      }
    }
  }

  init {
    mapper.propertyNamingStrategy = PropertyNamingStrategy.SNAKE_CASE
  }

  companion object {
    val X_AUTH_TOKEN = "X-AUTH-TOKEN"
    val dummyToken = "{\"userId\":10001,\"organizationId\":13304,\"proxyOrganizationId\":0,\"username\":\"testaccount\",\"token\":\"DUMMY_TOKEN\",\"internal\":true,\"inMaintenanceMode\":false,\"permissions\":[\"SCANCONFIG\"],\"features\":null,\"authorities\":null}"
    val LOGGER = LoggerFactory.getLogger(Client::class.java)
  }
}
