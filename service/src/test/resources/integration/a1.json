[{"evidenceId": "296b843e-5698-4f67-a004-70f8ddbce9c9", "evidencePaths": [{"filePath": "a1.zip#zip:a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar:::org/apache/commons/fileupload/util", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "a752b148f6e549741cb46b2c851d0cf5bc6b20205664ce218b88958004fdd9e7", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "94349cc1-9c70-40f5-9318-26dd4c6b752a", "evidencePaths": [{"filePath": "a1.zip#zip:a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar:::org/apache/commons/fileupload/portlet", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "90bad5e0fc0193236dddaafbd38b246c27f6c26542f625dd52fe56732279915a", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "39e34049-981d-4c7f-be86-690198125d62", "evidencePaths": [{"filePath": "a1.zip#zip:a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": "a10c06183fe21f3bb3dda3b5946b93db6e2ad5cc", "sha2": "b570cacc936c8b6ed4b7741219de8782a6a796c6f929e97625a888847a8df1f3", "bytecodeHash": "d3a1682c87cb8c88325695e995b400255c5e526e6fb4fa335b4b7ed27f8d73c9", "commitHash": null, "evidenceType": "JAR"}, {"evidenceId": "2e0b07dc-57cf-446e-8e10-bd5b37225bac", "evidencePaths": [{"filePath": "a1.zip#zip:a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar:::org/apache/commons/fileupload/servlet", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "dee850e219c97c8a3c48d8ec4bf36eda7053e9ecf5b1d0008105270da3b47af1", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "87789e73-5f56-4f30-a906-131fba6d1468", "evidencePaths": [{"filePath": "a1.zip#zip:a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar:::org/apache/commons/fileupload/disk", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "895b1f850c413a7a9206c399770575216e4686aabc359d6b46511f444684d5a6", "commitHash": null, "evidenceType": "BYTECODE"}]