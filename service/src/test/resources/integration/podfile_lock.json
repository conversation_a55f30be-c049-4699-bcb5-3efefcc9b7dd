[{"evidenceId": "665d4871-0046-4185-8e16-c24faa29c67a", "evidencePaths": [{"filePath": "podfile_lock.zip#zip:Podfile.lock", "lineNumber": 3, "dependencyPath": [], "filePathWithLineNumber": "podfile_lock.zip#zip:Podfile.lock#L3"}], "coordinates": {"coordinate1": "HockeyKit", "coordinate2": null, "version": "2.0.7", "platform": null, "scope": null, "coordinateType": "COCOAPODS"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "0fd39ac1-69b8-4fe1-b077-a35c14678228", "evidencePaths": [{"filePath": "podfile_lock.zip#zip:Podfile.lock", "lineNumber": 4, "dependencyPath": [], "filePathWithLineNumber": "podfile_lock.zip#zip:Podfile.lock#L4"}], "coordinates": {"coordinate1": "OpenSSL-Static", "coordinate2": null, "version": "1.0.2.c1", "platform": null, "scope": null, "coordinateType": "COCOAPODS"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "ab9956f6-c1e0-4ed8-a832-50be799d7d42", "evidencePaths": [{"filePath": "podfile_lock.zip#zip:Podfile.lock", "lineNumber": 9, "dependencyPath": [], "filePathWithLineNumber": "podfile_lock.zip#zip:Podfile.lock#L9"}], "coordinates": {"coordinate1": "Result", "coordinate2": null, "version": "3.1.0", "platform": null, "scope": null, "coordinateType": "COCOAPODS"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "05040a1c-7932-4780-b331-1b7942a52976", "evidencePaths": [{"filePath": "podfile_lock.zip#zip:Podfile.lock", "lineNumber": 2, "dependencyPath": [], "filePathWithLineNumber": "podfile_lock.zip#zip:Podfile.lock#L2"}], "coordinates": {"coordinate1": "Alamofire", "coordinate2": null, "version": "4.3.0", "platform": null, "scope": null, "coordinateType": "COCOAPODS"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "10cfd449-787a-4d27-b750-a212fb8d3544", "evidencePaths": [{"filePath": "podfile_lock.zip#zip:Podfile.lock", "lineNumber": 9, "dependencyPath": [], "filePathWithLineNumber": "podfile_lock.zip#zip:Podfile.lock#L9"}], "coordinates": {"coordinate1": "ReactiveSwift", "coordinate2": null, "version": "1.0.0", "platform": null, "scope": null, "coordinateType": "COCOAPODS"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "30384bb7-ff9a-4fbb-83a6-2aa69527dc24", "evidencePaths": [{"filePath": "podfile_lock.zip#zip:Podfile.lock", "lineNumber": 7, "dependencyPath": [], "filePathWithLineNumber": "podfile_lock.zip#zip:Podfile.lock#L7"}], "coordinates": {"coordinate1": "ReactiveCocoa", "coordinate2": null, "version": "5.0.0-rc.1", "platform": null, "scope": null, "coordinateType": "COCOAPODS"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}]