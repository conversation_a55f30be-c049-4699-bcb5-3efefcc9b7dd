apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    deployment.kubernetes.io/revision: "8"
  labels:
    app: agent-wrapper
  name: agent-wrapper
  namespace: $K8S_NAMESPACE
spec:
  replicas: $REPLICAS
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: agent-wrapper
  serviceName: agent-wrapper # statefulsets requires a headless service
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: agent-wrapper
      annotations:
        prometheus.io/path: '/actuator/prometheus'
        prometheus.io/port: '8080'
        prometheus.io/scrape: 'true'
    spec:
      containers:
      - env:
        - name: SCANCONFIG_TRUSTSTOREPASSWORD
          valueFrom:
            secretKeyRef:
              key: truststore-password
              name: agent-wrapper-secrets
        - name: SQS_QUEUE
          value: $SQS_QUEUE
        - name: SCAN_TIMEOUT
          value: $SCAN_TIMEOUT
        - name: SRCCLR_URL
          value: $SRCCLR_URL
        - name: SRCCLR_QUEUE
          value: $SRCCLR_QUEUE
        - name: <PERSON><PERSON><PERSON>_BUCKET
          value: $SRCCLR_BUCKET
        - name: SR<PERSON><PERSON>_ISHTTP
          value: "false"
        - name: SRCCLR_KEY
          valueFrom:
            secretKeyRef:
              key: srcclr-key
              name: agent-wrapper-secrets
        - name: SCANCONFIG_TRUSTSTOREPATH
          value: /cert/keystore/truststore.jks
        - name: SCANCONFIG_KEYSTOREPATH
          value: /cert/keystore/keystore.jks
        - name: SCANCONFIG_KEYSTOREPASSWORD
          valueFrom:
            secretKeyRef:
              key: keystore-password
              name: agent-wrapper-secrets
        - name: UNPACK_RECURSIVELY
          value: "true"
        - name: ROLE_ARN
          value: $S3_ASSUME_ROLE_ARN
        - name: ROLE_SESSION_NAME
          value: $S3_ASSUME_ROLE_SESSION_NAME
        - name: SPRING_PROFILES_ACTIVE
          value: $SPRING_PROFILES_ACTIVE
        - name: JAVA_OPTS
          value: |
            -Xms8G -Xmx8G -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.port=8888 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.local.only=false -Djava.rmi.server.hostname=localhost -Djava.security.egd=file:/dev/urandom -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heap-dump.hprof
        image: $POD_IMAGE
        imagePullPolicy: IfNotPresent
        name: agent-wrapper
        ports:
        - containerPort: 8080
          name: management
          protocol: TCP
        - containerPort: 8888
          name: jmx
          protocol: TCP
        readinessProbe:
          failureThreshold: 3
          httpGet:
            path: /actuator/health
            port: 8080
            scheme: HTTP
          initialDelaySeconds: 30
          periodSeconds: 10
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            memory: 13G
          requests:
            memory: 13G
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
          - name: secrets
            mountPath: "/secrets"
            readOnly: true
          - mountPath: /cert/keystore
            name: secret-volume
            readOnly: true
          - name: agent-wrapper-tmp
            mountPath: "/tmp"
      volumes:
        - name: secrets
          secret:
            secretName: agent-wrapper-secrets
        # use `kubectl create secret --namespace=unified generic scs-keystore --from-file=scs.jks`
        - name: secret-volume
          secret:
            defaultMode: 420
            secretName: scs-truststore
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
  volumeClaimTemplates:
  - metadata:
      name: agent-wrapper-tmp
    spec:
      accessModes: [ "ReadWriteOnce" ]
      storageClassName: "gp2-encrypted"
      resources:
        requests:
          storage: 12Gi
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  annotations:
    deployment.kubernetes.io/revision: "1"
  labels:
    app: agent-wrapper-dlq
  name: agent-wrapper-dlq
  namespace: $K8S_NAMESPACE
spec:
  replicas: $REPLICAS_DLQ
  revisionHistoryLimit: 5
  selector:
    matchLabels:
      app: agent-wrapper-dlq
  serviceName: agent-wrapper # statefulsets requires a headless service
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: agent-wrapper-dlq
      annotations:
        prometheus.io/path: '/actuator/prometheus'
        prometheus.io/port: '8080'
        prometheus.io/scrape: 'true'
    spec:
      containers:
        - env:
            - name: SCANCONFIG_TRUSTSTOREPASSWORD
              valueFrom:
                secretKeyRef:
                  key: truststore-password
                  name: agent-wrapper-secrets
            - name: SQS_QUEUE
              value: $SQS_QUEUE_DLQ
            - name: SCAN_TIMEOUT
              value: $SCAN_TIMEOUT_DLQ
            - name: SRCCLR_URL
              value: $SRCCLR_URL
            - name: SRCCLR_QUEUE
              value: $SRCCLR_QUEUE
            - name: SRCCLR_BUCKET
              value: $SRCCLR_BUCKET
            - name: SRCCLR_ISHTTP
              value: "false"
            - name: SRCCLR_KEY
              valueFrom:
                secretKeyRef:
                  key: srcclr-key
                  name: agent-wrapper-secrets
            - name: SCANCONFIG_TRUSTSTOREPATH
              value: /cert/keystore/truststore.jks
            - name: SCANCONFIG_KEYSTOREPATH
              value: /cert/keystore/keystore.jks
            - name: SCANCONFIG_KEYSTOREPASSWORD
              valueFrom:
                secretKeyRef:
                  key: keystore-password
                  name: agent-wrapper-secrets
            - name: UNPACK_RECURSIVELY
              value: "true"
            - name: ROLE_ARN
              value: $S3_ASSUME_ROLE_ARN
            - name: ROLE_SESSION_NAME
              value: $S3_ASSUME_ROLE_SESSION_NAME
            - name: SPRING_PROFILES_ACTIVE
              value: $SPRING_PROFILES_ACTIVE
            - name: JAVA_OPTS
              value: |
                -Xms24G -Xmx24G -Dcom.sun.management.jmxremote -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.port=8888 -Dcom.sun.management.jmxremote.ssl=false -Dcom.sun.management.jmxremote.local.only=false -Djava.rmi.server.hostname=localhost -Djava.security.egd=file:/dev/urandom -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=/tmp/heap-dump.hprof
          image: $POD_IMAGE
          imagePullPolicy: IfNotPresent
          name: agent-wrapper
          ports:
            - containerPort: 8080
              name: management
              protocol: TCP
            - containerPort: 8888
              name: jmx
              protocol: TCP
          readinessProbe:
            failureThreshold: 3
            httpGet:
              path: /actuator/health
              port: 8080
              scheme: HTTP
            initialDelaySeconds: 30
            periodSeconds: 10
            successThreshold: 1
            timeoutSeconds: 1
          resources:
            limits:
              memory: 28G
            requests:
              memory: 28G
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
            - name: secrets
              mountPath: "/secrets"
              readOnly: true
            - mountPath: /cert/keystore
              name: secret-volume
              readOnly: true
            - name: agent-wrapper-tmp
              mountPath: "/tmp"
      volumes:
        - name: secrets
          secret:
            secretName: agent-wrapper-secrets
        # use `kubectl create secret --namespace=unified generic scs-keystore --from-file=scs.jks`
        - name: secret-volume
          secret:
            defaultMode: 420
            secretName: scs-truststore
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
      affinity:
        nodeAffinity:
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
              - matchExpressions:
                  - key: beta.kubernetes.io/instance-type
                    operator: In
                    values:
                      - c5.4xlarge
      tolerations:
        - key: "agent"
          operator: "Equal"
          value: "yes"
          effect: "NoSchedule"
  volumeClaimTemplates:
    - metadata:
        name: agent-wrapper-tmp
      spec:
        accessModes: [ "ReadWriteOnce" ]
        storageClassName: "gp2-encrypted"
        resources:
          requests:
            storage: 12Gi

