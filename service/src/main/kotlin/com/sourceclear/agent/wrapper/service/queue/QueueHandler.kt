package com.sourceclear.agent.wrapper.service.queue

import com.github.kittinunf.result.Result
import com.google.common.util.concurrent.SimpleTimeLimiter
import com.sourceclear.agent.wrapper.scanconfig.client.Client
import com.sourceclear.agent.wrapper.scanconfig.client.ScanStatus
import com.sourceclear.agent.wrapper.scanconfig.client.ScanStatusRequest
import com.sourceclear.agent.wrapper.service.config.SourceClearCommunication
import com.sourceclear.agent.wrapper.service.objects.ScanRequestMessage
import com.sourceclear.agent.wrapper.service.queue.unpackers.*
import com.sourceclear.agent.wrapper.service.queue.unpackers.UnpackRecursive.Companion.unpackRecursively
import com.sourceclear.agent.wrapper.service.scan.UploadScanAttributes
import com.sourceclear.agent.wrapper.service.scan.UploadScanAttributes.Companion.dynamicAttributes
import com.sourceclear.agent.wrapper.service.utils.CloseableTempIO
import com.sourceclear.agent.wrapper.service.utils.Constants
import com.sourceclear.agent.wrapper.service.utils.aesCipherWith
import com.sourceclear.agent.wrapper.service.utils.containsExtensions
import com.sourceclear.api.data.evidence.Evidence
import com.sourceclear.api.data.evidence.EvidencePath
import com.sourceclear.api.data.evidence.EvidenceType
import com.sourceclear.api.data.match.VeracodeScan
import com.sourceclear.engine.component.ComponentEngineBuilder
import io.awspring.cloud.sqs.annotation.SqsListener
import org.apache.commons.codec.binary.Hex
import org.apache.commons.codec.digest.DigestUtils
import org.apache.commons.io.FileUtils
import org.apache.commons.io.FilenameUtils
import org.apache.commons.lang.time.DurationFormatUtils
import org.json.JSONObject
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.messaging.handler.annotation.Header
import org.springframework.messaging.handler.annotation.Payload
import org.springframework.stereotype.Component
import software.amazon.awssdk.core.SdkBytes
import software.amazon.awssdk.services.kms.KmsClient
import software.amazon.awssdk.services.kms.model.DecryptRequest
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.GetObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.S3Object
import java.io.EOFException
import java.io.InputStream
import java.nio.ByteBuffer
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.StandardCopyOption
import java.time.Duration
import java.util.Optional
import java.util.UUID
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.TimeoutException
import java.util.stream.Collectors
import java.util.zip.ZipException
import javax.crypto.CipherInputStream
import javax.crypto.Cipher


@Component
class QueueHandler(
  @Qualifier("binaries") private val s3Client: S3Client,
  private val kmsClient: KmsClient,
  private val scanconfigClient: Client,
  private val sourceClearCommunication: SourceClearCommunication,
  @Value("\${scan.timeout:6h}") private val timeout: Duration
) {

  data class EncryptionKey(val key: ByteArray, val iv: ByteArray)

  // we are only running one scan per instance of agent-wrapper
  private var executorService : ExecutorService = Executors.newSingleThreadExecutor()
  private var timeLimiter : SimpleTimeLimiter = SimpleTimeLimiter.create(executorService)
  private var filteredEvidence: Set<Evidence> = hashSetOf()

  /**
   * The main entry point to a scan.
   * Try not to augment/manipulate the message too much here.
   * The headers of the message should be only manipulated by SQS itself.
   * Particularly the receiveCount value signify the retry count of the message itself.
   * The upperlimit of this value is determined by SQS Dead-letter queue Configuration `Maximum receives`.
   * Please speak to the DevOps engineer if there is a need to change these configurations.
   *
   * @param scanRequestMessage the body of the message
   * @param receiveCount the message receive count number managed by AWS SQS
   */
  @SqsListener("\${sqs.queue}", factory = "sqsMessageListenerContainerFactory", maxConcurrentMessages = "1", maxMessagesPerPoll = "1" )
  fun scanRequestListener(
      @Payload scanRequestMessage: ScanRequestMessage,
      @Header(name = "ApproximateReceiveCount", defaultValue = "1") receiveCount: String
  ) {
    scanRequestListener(scanRequestMessage, isRecursiveEnv, receiveCount.toIntOrNull() ?: 1, timeout)
  }

  fun scanRequestListener(scanRequestMessage: ScanRequestMessage, isRecursive: Boolean, receiveCount: Int, timeout: Duration) {
    try {
      // We only limit the processing time for the unpacking and extraction of evidence
      timeLimiter.callWithTimeout(
        { scanRequest(scanRequestMessage, isRecursive, receiveCount) },
        timeout.toSeconds(),
        TimeUnit.SECONDS
      )
      // We allow the evidence to be sent outside the timeout window
      sendEvidence(scanRequestMessage, filteredEvidence)
    } catch (t: TimeoutException) {
      val exceptionMessage = "Timed out while scanning binaries - timeout: " +
          DurationFormatUtils.formatDurationWords(timeout.toMillis(), true, false) +
          ", scanId: ${scanRequestMessage.scanId}"
      LOGGER.warn(exceptionMessage)
      sendErrorToScanConfigService(scanRequestMessage, t)
      resetExec()
      // rethrow for dead letter queue to work
      throw TimeoutException(exceptionMessage)
    } catch (t: Throwable) {
      //
      // Outside of timeout exception, an exception during execution of SimpleTimeLimiter will result in the java.util.concurrent.ExecutionException
      //  being thrown. The actual cause will be contained in ExecutionException's cause
      //
      if (t.cause is ZipException || t.cause is EOFException) {
        // don't retry. These zip exceptions mean retrying will not help.
        // the latest version of commons-compress throws EOFException for unsupported LZFSE files
        LOGGER.error("Scan failure. Will not retry due to issue while processing scanId {}: {}", scanRequestMessage.scanId, t.message)
        sendErrorToScanConfigService(scanRequestMessage, t)
        // remove msg, no need to process to dead letter queue
      } else {
        sendErrorToScanConfigService(scanRequestMessage, t)
        resetExec()
        // rethrow for dead letter queue to work
        throw t
      }
    }
  }

  /**
   * Make sure no re-entry before we swap new executor successfully.
   *
   * When timeout happens for existing executor thread, it will continue blocking
   * the rest of the executor until timeout for them. See JIRA #SCAXF-2572 for more
   * detail.
   *
   * This method is synchronized but will NOT block SQS trigger the listener and dispatch more messages
   * for the limiter, because the message delivered to agent-wrapper in a round-robin approach, the 20+ agent-wrappers
   * will not be in a saturated state with this fix and should consume queue fairly quick, the 'not executed tasks'
   * should be very limited and safe to DLQ for further process.
   */
  fun resetExec() {
    try {
      if (!executorService.awaitTermination(1000, TimeUnit.MILLISECONDS)) {
        // list of tasks that never commenced execution
        val notExecutedTasks: List<Runnable> = executorService.shutdownNow()
        if (notExecutedTasks.isNotEmpty()) {
          //won't be matter too much because SQS invisible will send them to DLQ
          LOGGER.info("{} tasks never commenced execution!", notExecutedTasks.size)
        }
      }
    } catch (i: InterruptedException) {
      LOGGER.warn("Executor service was interrupted while being terminated!")
    }

    executorService = Executors.newSingleThreadExecutor()
    timeLimiter = SimpleTimeLimiter.create(executorService)
    filteredEvidence = hashSetOf()
  }

  fun scanRequest(scanRequestMessage: ScanRequestMessage, isRecursive: Boolean, receiveCount: Int) {
    val scanId = scanRequestMessage.scanId

    LOGGER.info("Scanning scan id: {}", scanId)
    LOGGER.debug("Scan request message: {}", scanRequestMessage)

    val encryptionKey = decodeAndMaybeDecryptEncryptionKey(scanRequestMessage)
        ?: throw Error("Unable to get encryption key and iv from scan request message")

    val (key, iv) = encryptionKey

    LOGGER.debug("sha of decrypted key = {}", DigestUtils.sha256Hex(key))
    LOGGER.debug("sha of decrypted iv = {}", DigestUtils.sha256Hex(iv))

    val cipher = aesCipherWith(key = key, iv = iv)

    val validFiles = validFiles(s3Client, scanRequestMessage.bucketName, scanRequestMessage.binaryPath)

    if (LOGGER.isDebugEnabled) {
      validFiles.forEach { s3file ->
        LOGGER.debug("s3 file: {}", s3file.toString())
      }
    }

    val isArchived = isArchive(validFiles)
    CloseableTempIO.createTempDirectory(scanRequestMessage.scanConfigId).use { dir ->
      validFiles.forEach { s3Object ->
        writeToDiskAndUnpack(
            s3Object = s3Object,
            scanRequestMessage = scanRequestMessage,
            into = dir,
            cipher = cipher,
            isArchived = isArchived,
            isRecursive = isRecursive
        )
      }
      val scanDir = determineScanDir(dir, isArchived)
      LOGGER.info("scanId: {} unpacked. Scanning in {}...", scanId, scanDir)
      val evidence = scan(scanDir, dynamicAttributes(receiveCount))

      if (LOGGER.isDebugEnabled) {
        for ((i, e) in evidence.withIndex()) {
          val jo = JSONObject()
          LOGGER.debug("evidence{} = {}", i, jo.put("body", e).toString())
        }
      }

      LOGGER.info("scanId: {}, appId: {}, number of evidence: {}.", scanId, scanRequestMessage.appId, evidence.size)
      filteredEvidence = deduplicateEvidence(normalizeAllPaths(evidence))
    }
  }

  private fun writeToDiskAndUnpack(
    s3Object: S3Object,
    scanRequestMessage: ScanRequestMessage,
    into: Path,
    cipher: Cipher,
    isArchived: Boolean,
    isRecursive: Boolean
  ) {
    LOGGER.debug("Fetching {}", s3Object.key())
    val basenameWithExtension = FilenameUtils.getName(s3Object.key())
    CloseableTempIO.createTempDirectory("archive-dir").use { tempDir ->
      val objectRequest = GetObjectRequest.builder()
                            .key(s3Object.key())
                            .bucket(scanRequestMessage.bucketName)
                            .build()
      val s3InputStream = s3Client.getObject(objectRequest).buffered()
      val archive = CipherInputStream(s3InputStream, cipher).use { inputStream ->
          writeToDisk(inputStream = inputStream, dir = tempDir, filename = basenameWithExtension)
      }
      LOGGER.debug("File fetched from {}", scanRequestMessage.binaryPath)
      LOGGER.debug("Size of the binary file {} bytes", FileUtils.sizeOf(archive.toFile()))
      unpackToDisk(
          filename = s3Object.key(),
          archive = archive,
          into = into,
          // the binary path here is xxx/xxx/xxx/<some_name>
          // and <some_name> must not be at the start of the evidence path
          // <some_name> can be either `binaries` or `scabinaries`
          basePath = scanRequestMessage.binaryPath,
          isArchivedFile = isArchived,
          recursive = isRecursive)
    }
  }

  private fun sendEvidence(scanRequestMessage: ScanRequestMessage, evidence: Set<Evidence>) {
    LOGGER.debug("scanId: {}, sending evidence to the SourceClear platform", scanRequestMessage.scanId)
    // TODO: Send the right values, remove additional nullable checks
    val veracodeScan = with(VeracodeScan.Builder()) {
      scanId = UUID.fromString(scanRequestMessage.scanId)
      appVersion = scanRequestMessage.appVerId
      appId = scanRequestMessage.appId.toString()
      appName = Optional.ofNullable(scanRequestMessage.appName).orElse("some name")
      accountId = scanRequestMessage.accountId.toString()
      env = scanRequestMessage.env
      sandboxId = scanRequestMessage.sandboxId
      analysisUnitId = scanRequestMessage.analysisUnitId
      analysisId = Optional.ofNullable(scanRequestMessage.analysisId).orElse(0)
      isSandbox = scanRequestMessage.sandbox
      binaryPath = scanRequestMessage.binaryPath
      jobId = scanRequestMessage.jobId
      isScaEnabled = scanRequestMessage.scaEnabled
      if (!scanRequestMessage.teamIds.isNullOrEmpty()) {
        addAllTeamIds(scanRequestMessage.teamIds)
      }
      addAllEvidence(evidence)
      policyLegacyId = scanRequestMessage.policyLegacyId
      policyVersion = Optional.ofNullable(scanRequestMessage.policyVersion).orElse(0)
      scanName = scanRequestMessage.scanName
      submittedBy = scanRequestMessage.submittedBy
      submitterName = scanRequestMessage.submitterName
      build()
    }

    when (val result = sourceClearCommunication.veracodeScan(veracodeScan)) {
      is Result.Success<*> -> {
        LOGGER.info("scanId: {}, Successfully sent evidence to SourceClear platform", scanRequestMessage.scanId)
      }
      is Result.Failure<*> -> {
        val err = result.error
        LOGGER.error("scanId: {}, Unable to send evidence to SourceClear platform", scanRequestMessage.scanId, err)
        throw err
      }
    }
  }

  private fun decodeAndMaybeDecryptEncryptionKey(scanRequestMessage: ScanRequestMessage): EncryptionKey? {
    return if ((scanRequestMessage.encryptKey != null && !scanRequestMessage.encryptKey.isBlank()) &&
        (scanRequestMessage.encryptIv != null && !scanRequestMessage.encryptIv.isBlank())) {
      EncryptionKey(Hex.decodeHex(scanRequestMessage.encryptKey), Hex.decodeHex(scanRequestMessage.encryptIv))
    } else if (scanRequestMessage.encryptedKey != null && !scanRequestMessage.encryptedKey.isBlank()) {
      val decryptedKey = decrypt(Hex.decodeHex(scanRequestMessage.encryptedKey))
      // See https://wiki.veracode.local/display/Eng/Static+Scans+AWS+KMS+Based+Encryption+Support
      // key = decryptedKey[0..31], iv = decryptedKey[32..47]
      EncryptionKey(decryptedKey.sliceArray(IntRange(0, 31)), decryptedKey.sliceArray(IntRange(32, 47)))
    } else {
      null
    }
  }

  private fun sendErrorToScanConfigService(scanRequestMessage: ScanRequestMessage, t: Throwable) {
    LOGGER.error("Unable to scan project for scanId: {}, appId: {}. cause:", scanRequestMessage.scanId, scanRequestMessage.appId, t)
    LOGGER.debug("ScanRequestMessage = {}", scanRequestMessage)
    LOGGER.info("Sending FAILED status to scanconfig service")
    val scanStatusRequest = ScanStatusRequest(scanRequestMessage.scanConfigId, scanRequestMessage.scanId, ScanStatus.FAILED, "${t.javaClass.canonicalName}: ${t.message}")
    when (val result = scanconfigClient.sendScanStatus(scanStatusRequest)) {
      is Result.Success -> {
        LOGGER.info("Successfully sent status to scanconfig service")
      }
      is Result.Failure -> {
        LOGGER.error("Unable to send status to scanconfig service, scanId: ${scanRequestMessage.scanId}, appId: ${scanRequestMessage.appId}", result.error.exception)
      }
    }
  }

  fun decrypt(ciphertext: ByteArray): ByteArray {
    val ciphertextBuffer = ByteBuffer.wrap(ciphertext)
    val decryptRequest = DecryptRequest.builder().ciphertextBlob(SdkBytes.fromByteBuffer(ciphertextBuffer)).build()
    val decryptResult = kmsClient.decrypt(decryptRequest)
    return decryptResult.plaintext().asByteArray()
  }

  companion object {

    private val LOGGER = LoggerFactory.getLogger(QueueHandler::class.java)

    private val isRecursiveEnv = System.getenv("UNPACK_RECURSIVELY") == "true"

    private val BINARY_FOLDER_NAMES = setOf("binaries", "scabinaries")

    fun writeToDisk(inputStream: InputStream, dir: Path, filename: String): Path {
      val dest = dir.resolve(filename)
      Files.copy(inputStream, dest, StandardCopyOption.REPLACE_EXISTING)
      return dest
    }

    fun unpackToDisk(filename: String,
                     archive: Path,
                     into: Path,
                     isArchivedFile: Boolean,
                     basePath: String = "",
                     recursive: Boolean = true): Path {
      // Create a temp directory for us to write the archive to. That's because we want to unzip it into a separate directory
      return CloseableTempIO.createTempDirectory("archive-dir").use {
        val basenameWithExtension = FilenameUtils.getName(filename)
        val targetDir = if (isArchivedFile) {
          // the isArchived function allows us to make this conclusion of using ZipUnpacker
          ZipUnpacker.instance.unpack(archive, into)
        } else {
          // basePath should consist of xxx/xxx/xxx/binaries or xxx/xxx/xxx/scabinaries
          val relativeDirectoryPath = filename
              // relativePath = filename - basePath  <- hence we remove basePath from filename
              .removePrefix(basePath)
              // relativeDirectoryPath = relativePath - basename <- hence we remove basename from relativePath
              .removeSuffix(basenameWithExtension)
              // remove starting slash just in case
              .removePrefix("/")
          // don't bother unpacking just move them
          // return the parent because we want a directory, not a possible file.
          moveFile(archive, into.resolve(relativeDirectoryPath)).parent
        }

        // un-archived files have one less nestingLevel.
        // Hence un-archived files would unpack one nestingLevel deeper than archived files
        // For Android nested package, we will extend search path
        if (recursive) {
          val maxDepth = if (containsExtensions(into.toFile(), Constants.ANDROID_PACKAGE_EXTENSIONS)) {
            Constants.MAX_DIR_DEPTH_ANDROID
          } else {
            Constants.MAX_DIR_DEPTH
          }
          unpackRecursively(targetDir, 2, maxDepth)
        }

        targetDir
      }
    }

    fun scan(targetDir: Path, dynamicAttributes: Map<String, Any> = dynamicAttributes()): Set<Evidence> {
      return ComponentEngineBuilder()
          // default attributes
          .withAttributes(UploadScanAttributes.staticAttributes)
          // attributes that change based on message
          .withAttributes(dynamicAttributes)
          .withEngineType(ComponentEngineBuilder.EngineType.QUICKSCAN)
          .withRecursive(true)
          .withProjectPath(targetDir.toFile())
          .build()
          .collect()
          .evidence ?: setOf()
    }

    /**
     * Determines whether the binary sent to us could possibly be a russian doll.
     * Example given, a `binaries` folder inside a `binaries.zip` archived file.
     * Peels only 1 layer if it is a russian doll.
     *
     * @param dir the supposed path to scan
     * @return the actual path to scan
     */
    private fun determineScanDir(dir: Path, isArchivedFile: Boolean): Path {
      // this function is mainly for archived files.
      if (!isArchivedFile) {
        return dir
      }
      // if we can't determine a binaries folder that is made by us instead of the user
      // then don't bother trying to determine it
      if (Files.list(dir).count() != 1L) {
        return dir
      }
      return BINARY_FOLDER_NAMES
          .map { dir.resolve(it) }
          .firstOrNull { it.toFile().exists() }
          ?: dir
    }

    /**
     * List max of 1000 s3 objects to be returned.
     */
    private fun validFiles(s3Client: S3Client, bucket: String, binaryPath: String) =
       s3Client.listObjectsV2(
        ListObjectsV2Request.builder()
          .bucket(bucket)
          .prefix(binaryPath)
          .build()
      ).contents().stream().filter { it.size() != 0L }.collect(Collectors.toList())

    /**
     * Determines whether the file sent to us is an archived file.
     * We split how we process the file in `writeToDiskAndUnpack`.
     *
     * @param listOfS3Files list of objects from `validFiles`
     * @return true when the received binary is an archive
     */
    private fun isArchive(listOfS3Files: List<S3Object>): Boolean {
      val isSingular = listOfS3Files.size == 1
      val containsZipAesExtension = listOfS3Files.filter { s3Object ->
        s3Object.key().endsWith(".zip.aes")
      }.isNotEmpty()
      return isSingular && containsZipAesExtension
    }

    fun determineUnpacker(file: Path, unpackers: List<Unpacker>): Unpacker? =
        unpackers.firstOrNull { it.matches(file, LOGGER) }

    private fun moveFile(file: Path, into: Path): Path {
      val intoWithFilename = when (file.toFile().extension) {
        "aes" -> into.resolve(file.toFile().nameWithoutExtension)
        else -> into.resolve(file.fileName)
      }
      Files.createDirectories(into)
      LOGGER.debug("Moving file {} to {}", file, intoWithFilename)
      return Files.move(file, intoWithFilename)
    }

    fun normalizeAllPaths(evidenceSet: Set<Evidence>): Set<Evidence> {
      for (evidence in evidenceSet) {
        val evidencePaths = evidence.evidencePaths.map {
          return@map EvidencePath(
            normalizePath(it.filePath),
            it.lineNumber, it.dependencyPath
          )
        }.map {
          // group evidencePaths by filePath thus merging duplicates
          it.filePath to it
        }
        // collect the map values to a list
        .toMap().values.stream().toList()

        evidence.evidencePaths.clear()
        evidence.evidencePaths.addAll(evidencePaths)
      }

      return evidenceSet
    }

    fun deduplicateEvidence(evidenceSet: Set<Evidence>): Set<Evidence> {
      val uniqueFilePaths = HashSet<Set<String>>()
      val uniqueEvidenceSet = HashSet<Evidence>()
      for (evidence in evidenceSet) {
        // replace the evidencePaths with the merged version
        val filePathSet = evidence.evidencePaths.map { it.filePath }.toSet()

        val isUnpackResult = filePathSet.any { it.contains(UNPACK_DELIMITER) }

        val isFatJarEvidence = evidence.evidenceType == EvidenceType.JAR ||
            evidence.evidenceType == EvidenceType.AAR ||
            evidence.evidenceType == EvidenceType.BYTECODE

        if (isUnpackResult && isFatJarEvidence) {
          /*
           * If two or more evidence have the same evidence paths. i.e. Same array length and same evidence paths,
           * eliminate duplicate evidence.
           */
          if (!uniqueFilePaths.contains(filePathSet)) {
            uniqueFilePaths.add(filePathSet)
            uniqueEvidenceSet.add(evidence)
          }
        } else {
          uniqueEvidenceSet.add(evidence)
        }
      }
      return uniqueEvidenceSet
    }

    /**
     * Replaces occurrences of {@link UNPACK_DIR_SUFFIX} in filePath with {@link UNPACK_DELIMITER}.
     *
     * @param filePath the evidencePath's filepath
     */
    private fun normalizePath(filePath: String): String {
      return filePath.replace("$UNPACK_DIR_SUFFIX/", UNPACK_DELIMITER)
    }
  }
}