package com.sourceclear.agent.wrapper.http

import com.github.kittinunf.result.Result
import java.io.IOException
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.net.http.HttpResponse


sealed class HttpError(open val exception: java.lang.Exception) : Exception()
data class Non2XXError(val statusCode: Int, val body: String, override val exception: java.lang.Exception): HttpError(exception)
data class GenericHttpError(override val exception: java.lang.Exception): HttpError(exception)

fun HttpRequest.Builder.putJson(payload: String): HttpRequest.Builder {
  return this.PUT(HttpRequest.BodyPublishers.ofString(payload))
      .setHeader("Content-Type", "application/json")
}

fun HttpRequest.Builder.postJson(payload: String): HttpRequest.Builder {
  return this.POST(HttpRequest.BodyPublishers.ofString(payload))
      .setHeader("Content-Type", "application/json")
}

fun HttpClient.send(request: HttpRequest): Result<String, HttpError> {
  val response: HttpResponse<String>?
  try {
    response = this.send(request, HttpResponse.BodyHandlers.ofString())
  } catch (e: Exception) {
    return Result.error(GenericHttpError(e))
  }
  val body = response.body()
  val notHttpSuccess = response.statusCode() !in 200..299
  return if (notHttpSuccess) {
    val msg = "HTTP request failed with non 2XX status code. status code: %s. message: %s".format(response.statusCode(), body)
    Result.error(Non2XXError(response.statusCode(), body, IOException(msg)))
  } else {
    Result.success(body)
  }
}
