[{"evidenceId": "c230a22a-2a9b-4918-b5d6-45375251e00d", "evidencePaths": [{"filePath": "pipfile_lock.zip#zip:Pipfile.lock", "lineNumber": 34, "dependencyPath": [], "filePathWithLineNumber": "pipfile_lock.zip#zip:Pipfile.lock#L34"}], "coordinates": {"coordinate1": "pyasn1", "coordinate2": null, "version": "0.4.8", "platform": null, "scope": null, "coordinateType": "PYPI"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "31ea028f-1dc0-4d7d-8bbe-5db62e42c8d6", "evidencePaths": [{"filePath": "pipfile_lock.zip#zip:Pipfile.lock", "lineNumber": 25, "dependencyPath": [], "filePathWithLineNumber": "pipfile_lock.zip#zip:Pipfile.lock#L25"}], "coordinates": {"coordinate1": "feedparser", "coordinate2": null, "version": "5.1.1", "platform": null, "scope": null, "coordinateType": "PYPI"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "7eac6cad-2c1c-4af1-9559-e49ee0bb0bb1", "evidencePaths": [{"filePath": "pipfile_lock.zip#zip:Pipfile.lock", "lineNumber": 17, "dependencyPath": [], "filePathWithLineNumber": "pipfile_lock.zip#zip:Pipfile.lock#L17"}], "coordinates": {"coordinate1": "django", "coordinate2": null, "version": "1.7.1", "platform": null, "scope": null, "coordinateType": "PYPI"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "04f576df-491b-4865-8f56-f6b8aeba0318", "evidencePaths": [{"filePath": "pipfile_lock.zip#zip:Pipfile.lock", "lineNumber": 67, "dependencyPath": [], "filePathWithLineNumber": "pipfile_lock.zip#zip:Pipfile.lock#L67"}], "coordinates": {"coordinate1": "requests", "coordinate2": null, "version": "2.2.1", "platform": null, "scope": null, "coordinateType": "PYPI"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "f2e64bfa-dec1-4951-ac00-07eddd3e25c4", "evidencePaths": [{"filePath": "pipfile_lock.zip#zip:Pipfile.lock", "lineNumber": 52, "dependencyPath": [], "filePathWithLineNumber": "pipfile_lock.zip#zip:Pipfile.lock#L52"}], "coordinates": {"coordinate1": "pycrypto", "coordinate2": null, "version": "2.4", "platform": null, "scope": null, "coordinateType": "PYPI"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "28148642-b794-4a52-93d0-2e6540354087", "evidencePaths": [{"filePath": "pipfile_lock.zip#zip:Pipfile.lock", "lineNumber": 59, "dependencyPath": [], "filePathWithLineNumber": "pipfile_lock.zip#zip:Pipfile.lock#L59"}], "coordinates": {"coordinate1": "pyjwt", "coordinate2": null, "version": "0.4.2", "platform": null, "scope": null, "coordinateType": "PYPI"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "40b5a7a1-b65b-4380-94bf-8cdcf37ca9af", "evidencePaths": [{"filePath": "pipfile_lock.zip#zip:Pipfile.lock", "lineNumber": 75, "dependencyPath": [], "filePathWithLineNumber": "pipfile_lock.zip#zip:Pipfile.lock#L75"}], "coordinates": {"coordinate1": "rsa", "coordinate2": null, "version": "3.4", "platform": null, "scope": null, "coordinateType": "PYPI"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}]