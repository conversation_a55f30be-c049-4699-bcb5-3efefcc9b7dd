[{"evidenceId": "c05d9f14-f4df-4735-a128-22f902e5b318", "evidencePaths": [{"filePath": "composer.zip#zip:composer.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "composer.zip#zip:composer.lock"}], "coordinates": {"coordinate1": "appserver-io/http", "coordinate2": null, "version": "1.1.6", "platform": null, "scope": "packages", "coordinateType": "PACKAGIST"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "16f5f3be-9d44-4035-9f39-55cfea27476b", "evidencePaths": [{"filePath": "composer.zip#zip:composer.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "composer.zip#zip:composer.lock"}], "coordinates": {"coordinate1": "ircmaxell/password-compat", "coordinate2": null, "version": "v1.0.4", "platform": null, "scope": "packages", "coordinateType": "PACKAGIST"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "a7619082-cecf-4a22-97d5-e9d7acc824d8", "evidencePaths": [{"filePath": "composer.zip#zip:composer.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "composer.zip#zip:composer.lock"}], "coordinates": {"coordinate1": "appserver-io-psr/http-message", "coordinate2": null, "version": "1.4.1", "platform": null, "scope": "packages", "coordinateType": "PACKAGIST"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "59db7053-e72f-4670-8e91-e6153b18b6f8", "evidencePaths": [{"filePath": "composer.zip#zip:composer.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "composer.zip#zip:composer.lock"}], "coordinates": {"coordinate1": "moodle/moodle", "coordinate2": null, "version": "v2.6.2", "platform": null, "scope": "packages", "coordinateType": "PACKAGIST"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}]