[{"evidenceId": "6caaaba1-a7e7-4140-aad5-e51d419448af", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "d3", "coordinate2": null, "version": "3.5.17", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "0c467745-b5e4-4c87-8d16-7c2e1f2f1178", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "brace-expansion", "coordinate2": null, "version": "1.1.8", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "bdb27bde-3295-48f3-82c4-cd77432b1450", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "concat-map", "coordinate2": null, "version": "0.0.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "647ebb32-129a-43b7-8ad8-2de6d0e05f02", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "hawk", "coordinate2": null, "version": "2.3.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "33fc979f-774c-48f0-87d0-392aae88f8d3", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "boom", "coordinate2": null, "version": "2.10.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "53cba29a-f910-4625-9d82-9977e7ebff81", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "cryptiles", "coordinate2": null, "version": "2.0.5", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "a3f37be1-4e08-4133-af82-e101b0b0c518", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "balanced-match", "coordinate2": null, "version": "0.4.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "77116afb-f035-474d-b44e-158c24c9effc", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "sntp", "coordinate2": null, "version": "1.0.9", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "6bf638dc-8a0a-45b3-922a-a062470e4e0b", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "minimatch", "coordinate2": null, "version": "2.0.10", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "3710a06c-9a6d-4f2a-8bb1-348d380956b0", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "balanced-match", "coordinate2": null, "version": "1.0.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "323453dc-b04d-410c-a900-c2967c1529b8", "evidencePaths": [{"filePath": "package_lock.zip#zip:package-lock.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "package_lock.zip#zip:package-lock.json"}], "coordinates": {"coordinate1": "hoek", "coordinate2": null, "version": "2.16.3", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}]