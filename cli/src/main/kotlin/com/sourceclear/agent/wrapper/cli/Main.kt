package com.sourceclear.agent.wrapper.cli

import com.sourceclear.agent.wrapper.sourceclear.client.SourceClearClient
import com.sourceclear.api.data.evidence.Evidence
import com.sourceclear.api.data.match.VeracodeScan
import com.sourceclear.engine.component.ComponentEngineBuilder
import java.net.URI
import java.nio.file.Path
import java.nio.file.Paths
import java.util.UUID

fun main(args: Array<String>) {
  if (args.isEmpty()) {
    println("No target directory supplied.")
  }

  val targetDir = Paths.get(args.first())
  val evidence = scan(targetDir)
  println("Size = ${evidence.size}")

  val veracodeScan = with(VeracodeScan.Builder()) {
    scanId = UUID.randomUUID()
    appVersion = 159007
    appId = "166681"
    appName = "some name"
    accountId = "15692"
    env = "malachite"
    addAllEvidence(evidence)
    build()
  }

  println("scanId = ${veracodeScan.scanId}")

  val client = SourceClearClient(URI("https://api.ops2.srcclr.io"), "-----BEGIN PRIVATE KEY-----\n" +
      "-----END PRIVATE KEY-----")
  val t = time {
    val res = client.veracodeScan(veracodeScan)
    println(res.component1())
  }

  println("Request took $t ms")
}

fun time(f: () -> Unit): Long {
  val start = System.currentTimeMillis()
  f.invoke()
  val end = System.currentTimeMillis()
  return end - start
}

fun scan(targetDir: Path): Set<Evidence> {
  val srcclrScanAttributes = mapOf<String, Any>(
      Pair(ComponentEngineBuilder.FAT_JAR, true),
      Pair(ComponentEngineBuilder.NPM_SCOPE, "production"),
      Pair(ComponentEngineBuilder.NODE_MODULES, true),
      Pair(ComponentEngineBuilder.BOWER_COMPONENTS, true)
  )
  return ComponentEngineBuilder().withAttributes(srcclrScanAttributes)
      .withEngineType(ComponentEngineBuilder.EngineType.QUICKSCAN)
      .withRecursive(true)
      .withProjectPath(targetDir.toFile())
      .build()
      .collect()
      .evidence ?: setOf()
}
