package com.sourceclear.agent.wrapper.service.config

import java.net.URI
import nl.altindag.ssl.SSLFactory
import nl.altindag.ssl.util.Apache4SslUtils
import org.apache.http.conn.socket.ConnectionSocketFactory
import org.bouncycastle.jcajce.provider.BouncyCastleFipsProvider
import org.bouncycastle.jsse.provider.BouncyCastleJsseProvider
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.annotation.Bean
import org.springframework.stereotype.Component
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.AwsCredentialsProvider
import software.amazon.awssdk.auth.credentials.AwsCredentialsProviderChain
import software.amazon.awssdk.auth.credentials.DefaultCredentialsProvider
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.http.apache.ApacheHttpClient
import software.amazon.awssdk.http.SdkHttpClient
import software.amazon.awssdk.http.async.SdkAsyncHttpClient
import software.amazon.awssdk.http.nio.netty.NettyNioAsyncHttpClient
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.regions.providers.DefaultAwsRegionProviderChain
import software.amazon.awssdk.services.kms.KmsClient
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.sqs.SqsAsyncClient
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sts.StsClient
import software.amazon.awssdk.services.sts.auth.StsAssumeRoleCredentialsProvider
import software.amazon.awssdk.services.sts.model.AssumeRoleRequest

@Component
class AwsConfig(@Value("\${aws.sslFactory.tls-version:TLSv1.2}") val tlsVersion: String,
                @Value("\${aws.fips.endpoint:false}") val fipsEndpoint: Boolean) {

  companion object {
    private val LOGGER = LoggerFactory.getLogger(Companion::class.java)
  }

  @Bean
  fun awsCredentials(@Value("\${spring.cloud.aws.credentials.access-key:#{null}}") accessKey: String?,
                     @Value("\${spring.cloud.aws.credentials.secret-key:#{null}}") secretKey: String?): AwsCredentialsProvider {
    return if(accessKey != null && secretKey != null ) {
      AwsCredentialsProviderChain.builder().addCredentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(accessKey, secretKey))).build()
    } else {
      AwsCredentialsProviderChain.builder().addCredentialsProvider(DefaultCredentialsProvider.create()).build()
    }
  }

  @Bean
  fun region(@Value("\${spring.cloud.aws.region.static:#{null}}") region: String?): Region {
    return if(region != null) {
      Region.of(region)
    } else {
      DefaultAwsRegionProviderChain.builder().build().region
    }
  }

  @Bean
  fun kmsClient( @Qualifier("sdkHttpClient") sdkHttpClient: SdkHttpClient,
                 @Value("#{'\${spring.cloud.aws.kms.endpoint:\${spring.cloud.aws.endpoint:}}'}") kmsEndpoint: String,
                 awsCredentialsProvider: AwsCredentialsProvider, region: Region): KmsClient {
    val builder = KmsClient.builder()
      .credentialsProvider(awsCredentialsProvider)
      .httpClient(sdkHttpClient)
    builder.region(region)
    if(fipsEndpoint){
      builder.fipsEnabled(true)
      val fipsUri = URI.create(String.format("https://kms-fips.%s.amazonaws.com", region.id()))
      builder.endpointOverride(fipsUri)
      if(kmsEndpoint.isNotBlank()) {
        LOGGER.info(String.format("FipsEndpoint is enabled, ignore kms.endpoint property and use default KMS fips endpoint, %s", fipsUri))
      }
    }
    else{
      if(kmsEndpoint.isNotBlank()) {
        builder.endpointOverride(URI.create(kmsEndpoint))
        LOGGER.info(String.format("Using %s as KMS endpoint", kmsEndpoint))
      }
      builder.fipsEnabled(false)
    }
    return builder.build()
  }

  @Bean
  @Qualifier("binaries")
  fun binariesS3Client(@Value("\${role.arn:#{null}}") roleArn: String?,
                       @Value("\${role.session.name:#{null}}") roleSessionName: String?,
                       @Value("#{'\${spring.cloud.aws.s3.endpoint:\${spring.cloud.aws.endpoint:}}'}") s3Endpoint: String,
                       @Qualifier("sdkHttpClient") sdkHttpClient: SdkHttpClient,
                       awsCredentialsProvider: AwsCredentialsProvider,
                       region: Region): S3Client {

    val credentialsProvider = if (roleArn != null && roleSessionName != null) {
      val stsBuilder = StsClient.builder()
        .credentialsProvider(awsCredentialsProvider)
        .httpClient(sdkHttpClient)
      stsBuilder.region(region)
      val stsClient = stsBuilder.build()
      StsAssumeRoleCredentialsProvider.builder()
        .refreshRequest(AssumeRoleRequest.builder()
        .roleArn(roleArn)
        .roleSessionName(roleSessionName)
        .build())
        .stsClient(stsClient)
        .build()
    } else {
      awsCredentialsProvider
    }

    val s3Builder = S3Client.builder()
      .credentialsProvider(credentialsProvider)
      .httpClient(sdkHttpClient)
    s3Builder.region(region)

    if(fipsEndpoint){
      s3Builder.fipsEnabled(true)
      val fipsUri = String.format("https://s3-fips.%s.amazonaws.com", region.id())
      s3Builder.endpointOverride(URI.create(fipsUri))
      if(s3Endpoint.isNotBlank()) {
        LOGGER.info(String.format("FipsEndpoint is enabled, ignore s3.endpoint property and use default S3 fips endpoint, %s, for 'binaries' S3 Client", fipsUri))
      }
    } else {
      if(s3Endpoint.isNotBlank()) {
        s3Builder.endpointOverride(URI.create(s3Endpoint))
        LOGGER.info(String.format("Using %s as S3 endpoint for 'binaries' S3 Client", s3Endpoint))
      }
    }

    return s3Builder.build()
  }

  @Bean
  @Qualifier("queueMessage")
  fun queueMessageS3Client(@Qualifier("sdkHttpClient") sdkHttpClient: SdkHttpClient,
                           @Value("#{'\${spring.cloud.aws.s3.endpoint:\${spring.cloud.aws.endpoint:}}'}") s3Endpoint: String,
                           awsCredentialsProvider: AwsCredentialsProvider, region: Region): S3Client {
    val s3Builder = S3Client.builder()
      .credentialsProvider(awsCredentialsProvider)
      .httpClient(sdkHttpClient)
      .region(region)
    if(fipsEndpoint) {
      s3Builder.fipsEnabled(true)
      val fipsUri = URI.create(String.format("https://s3-fips.%s.amazonaws.com", region.id()))
      s3Builder.endpointOverride(fipsUri)
      if(s3Endpoint.isNotBlank()) {
        LOGGER.info(String.format("FipsEndpoint is enabled, ignore s3.endpoint property and use default S3 fips endpoint, %s, for 'queueMessage' S3 Client", fipsUri))
      }
    } else {
      s3Builder.fipsEnabled(false)
      if(s3Endpoint.isNotBlank()) {
        s3Builder.endpointOverride(URI.create(s3Endpoint))
        LOGGER.info(String.format("Using %s as S3 endpoint for the 'queueMessage' S3 Client", s3Endpoint))
      }
    }
    return s3Builder.build()
  }

  @Bean
  @Qualifier("sqsClient")
  fun sqsClient(@Qualifier("sdkHttpClient") sdkHttpClient: SdkHttpClient,
                @Value("#{'\${spring.cloud.aws.sqs.endpoint:\${spring.cloud.aws.endpoint:}}'}") sqsEndpoint: String,
                awsCredentialsProvider: AwsCredentialsProvider, region: Region): SqsClient {
    val sqsClientBuilder = SqsClient.builder()
      .credentialsProvider(awsCredentialsProvider)
      .region(region)
      .httpClient(sdkHttpClient)
    if(fipsEndpoint) {
      sqsClientBuilder.fipsEnabled(true)
      val sqsUri: String = if(region.id().startsWith("us-gov", true)) {
        String.format("https://sqs.%s.amazonaws.com", region.id())
      } else {
        String.format("https://sqs-fips.%s.amazonaws.com", region.id())
      }
      sqsClientBuilder.endpointOverride(URI.create(sqsUri))
      if(sqsEndpoint.isNotBlank()) {
        LOGGER.info(String.format("FipsEndpoint is enabled, ignore sqs.endpoint property and use default SQS fips endpoint, %s", sqsUri))
      }
    } else {
      sqsClientBuilder.fipsEnabled(false)
      if(sqsEndpoint.isNotBlank()) {
        sqsClientBuilder.endpointOverride(URI.create(sqsEndpoint))
        LOGGER.info(String.format("Using %s as SQS endpoint for SQS client", sqsEndpoint))
      }
    }

    return sqsClientBuilder.build()
  }

  @Bean
  @Qualifier("sqsAsyncClient")
  fun sqsAsyncClient(@Qualifier("sdkAsyncHttpClient") sdkAsyncHttpClient: SdkAsyncHttpClient,
                     @Value("#{'\${spring.cloud.aws.sqs.endpoint:\${spring" +
                           ".cloud.aws.endpoint:}}'}") sqsEndpoint: String,
                     awsCredentialsProvider: AwsCredentialsProvider, region: Region): SqsAsyncClient {
    val sqsAsyncClientBuilder = SqsAsyncClient.builder()
      .credentialsProvider(awsCredentialsProvider)
      .httpClient(sdkAsyncHttpClient)
      .region(region)
    if(fipsEndpoint) {
      sqsAsyncClientBuilder.fipsEnabled(true)
      val sqsUri: String = if(region.id().startsWith("us-gov", true)) {
        String.format("https://sqs.%s.amazonaws.com", region.id())
      } else {
        String.format("https://sqs-fips.%s.amazonaws.com", region.id())
      }
      sqsAsyncClientBuilder.endpointOverride(URI.create(sqsUri))

      if(sqsEndpoint.isNotBlank()) {
        LOGGER.info(String.format("FipsEndpoint is enabled, ignore sqs.endpoint property and use default SQS fips endpoint, %s", sqsUri))
      }
    } else {
      sqsAsyncClientBuilder.fipsEnabled(false)
      if(sqsEndpoint.isNotBlank()) {
        sqsAsyncClientBuilder.endpointOverride(URI.create(sqsEndpoint))
        LOGGER.info(String.format("Using %s as SQS endpoint for SQS Async Client", sqsEndpoint))
      }
    }

    return sqsAsyncClientBuilder.build()
  }

  /**
   * Get the Apache Http Client
   */
  @Bean
  @Qualifier("sdkHttpClient")
  fun getApacheHttpClient(): SdkHttpClient {
    return ApacheHttpClient.builder()
            .socketFactory(getApacheSocketFactory())
            .build()
  }

  @Bean
  @Qualifier("sdkAsyncHttpClient")
  private fun getAsyncHttpClient(): SdkAsyncHttpClient {
    //TODO: use BouncyCastle JSSE Provider.
    return NettyNioAsyncHttpClient.builder().build()
  }

  /**
   * Build the Apache Socket factory for TLS connection
   */
  private fun getApacheSocketFactory(): ConnectionSocketFactory {
    return Apache4SslUtils.toSocketFactory(getSslFactory())
  }

  /**
   * Build the ssl factory for SSL connection .
   */
  private fun getSslFactory(): SSLFactory {
    return SSLFactory.builder()
      .withDefaultTrustMaterial()
      .withSecurityProvider(BouncyCastleJsseProvider(true, BouncyCastleFipsProvider()))
      .withProtocols(tlsVersion)
      .build()
  }
}
