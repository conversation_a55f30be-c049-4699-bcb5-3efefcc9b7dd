# Intro
This folder is originally designed to place terraform code for localstack. It is NOT for stage or production terraform code. 
If you are looking for stage or production terraform code for, please see https://gitlab.laputa.veracode.io/sca/sca-terraform/sca-stack-setup.

# Easy aws resource creation
Run the `setupLocalEnvForAgentWrapper.sh` script to bring up localstack and aws resources in localstack.

Run the `destroyLocalEnvForAgentWrapper.sh` script to destroy localstack and the aws resources in it.

# Running terraform

`terraform init`

`terraform plan`

`terraform apply --auto-approve`

`terraform destroy --auto-approve`
Alternatively, you can stop localstack.