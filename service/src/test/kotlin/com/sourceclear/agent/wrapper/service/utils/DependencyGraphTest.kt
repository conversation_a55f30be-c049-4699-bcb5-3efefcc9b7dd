package com.sourceclear.agent.wrapper.service.utils

import com.sourceclear.agent.wrapper.service.queue.QueueHandler
import com.sourceclear.agent.wrapper.service.scan.UploadScanAttributes
import com.sourceclear.api.data.evidence.Evidence
import com.sourceclear.api.data.evidence.EvidencePath
import com.sourceclear.engine.common.ScanDirectives
import org.junit.jupiter.api.Test
import java.nio.file.Files
import java.nio.file.Paths
import kotlin.test.assertFalse

class DependencyGraphTest {
  private val filename = "example-ruby.zip"
  private val testArchive = Paths.get(QueueHandler::class.java.classLoader.getResource(filename)!!.toURI())

  @Test
  internal fun dependencyGraphByDefaultTest() {
    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = true
      ).let {
        assertFalse { QueueHandler.scan(it).flatMap(Evidence::getEvidencePaths).flatMap(EvidencePath::getDependencyPath).isEmpty() }
      }
    }
  }

  @Test
  internal fun withDependencyGraphTest() {
    val attributes = UploadScanAttributes.staticAttributes.toMutableMap()
    attributes[ScanDirectives.NO_DEPENDENCY_GRAPH] = false
    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = true
      ).let {
        assertFalse { QueueHandler.scan(it, attributes).flatMap(Evidence::getEvidencePaths).flatMap(EvidencePath::getDependencyPath).isEmpty() }
      }
    }
  }
}
