package com.sourceclear.agent.wrapper.service.utils

import org.junit.Test
import java.nio.file.Files
import kotlin.test.assertFalse

class CloseableTempIOTest {

  /**
   * If to debug unzipped resource in pod, add @Disabled to ignore this test
   * once you comment out TempResource's cleanup() method
   */
  @Test
  fun testTempDirectoryCleanup() {
    val outerDir = CloseableTempIO.createTempDirectory("test")
    outerDir.use {
      Files.createTempFile(it, "my-test", ".empty")

      val inner = CloseableTempIO.createTempDirectory(it, "inner")
      Files.createTempFile(inner.path, "my-test", ".empty")
      Files.createTempFile(inner.path, "my-test", ".empty")

    }
    assertFalse { Files.exists(outerDir.path) }
  }
}
