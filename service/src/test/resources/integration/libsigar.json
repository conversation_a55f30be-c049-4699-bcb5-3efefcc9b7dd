[{"evidenceId": "072f83ad-aa44-4432-91f5-a18d56a50f23", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-ia64-linux.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-ia64-linux.so"}], "coordinates": {"coordinate1": "libsigar-ia64-linux.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "4155a96df54d1c25fc3e9fdfa86cb6e87ead271df9070ef3bf980835eb52719d", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "b6e0c70e-bacf-4e03-aa46-9c57b78cc620", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-ppc-linux.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-ppc-linux.so"}], "coordinates": {"coordinate1": "libsigar-ppc-linux.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "326a76cc2c4ebfe3abe85cf9f76507b8640b52bfab7394b4217b8e3af4c31873", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "fea7031d-825e-4dcf-9fb3-f5f11eeb6e36", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-x86-freebsd-6.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-x86-freebsd-6.so"}], "coordinates": {"coordinate1": "libsigar-x86-freebsd-6.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "cbcbc292b9367f546789384d0af3a3cea904f91d2164eb08b59296ebac0e9a9c", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "ccc4ddd4-cbcc-4b5f-9879-2451a5a60925", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-amd64-freebsd-6.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-amd64-freebsd-6.so"}], "coordinates": {"coordinate1": "libsigar-amd64-freebsd-6.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "fa3cfc818010b33a6c22315d0adb1ae2d6bff38d26b992fd8298d1bec0e83934", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "3bd7e9ca-bf9d-4afc-ad3a-2f69935b4446", "evidencePaths": [{"filePath": "libkvm.so.3", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libkvm.so.3"}], "coordinates": {"coordinate1": "libkvm.so", "coordinate2": null, "version": "3", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "38efd8b8-a23a-41bb-8d9f-9c8bb6cd781e", "evidencePaths": [{"filePath": "libkvm.so.2", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libkvm.so.2"}], "coordinates": {"coordinate1": "libkvm.so", "coordinate2": null, "version": "2", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "5a9e628d-e9e3-4dc4-92f6-d6f450617969", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-sparc64-solaris.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-sparc64-solaris.so"}], "coordinates": {"coordinate1": "libsigar-sparc64-solaris.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "ad336149c85a33765a7dec38735ce768e1ef925405c1e5c617558ece6bd934e9", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "f892c247-1722-4920-8c4b-989cfb3b42d5", "evidencePaths": [{"filePath": "libc.so.6", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libc.so.6"}], "coordinates": {"coordinate1": "libc.so", "coordinate2": null, "version": "6", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "3e905a93-8975-4b9e-8f2b-9428f7116f26", "evidencePaths": [{"filePath": "libkstat.so.1", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libkstat.so.1"}], "coordinates": {"coordinate1": "libkstat.so", "coordinate2": null, "version": "1", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "7a2c6a55-29e0-4c34-995a-c76729da85c7", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-s390x-linux.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-s390x-linux.so"}], "coordinates": {"coordinate1": "libsigar-s390x-linux.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "65b946cc5d3357f84d4e7ae2532479acd2131351be9a398ad1f345bb2e08e6d2", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "91ad7c87-9040-4788-8f10-42881549a5c9", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-amd64-linux.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-amd64-linux.so"}], "coordinates": {"coordinate1": "libsigar-amd64-linux.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "0007a9d9cf4f6e4ce8ccc5a762a1de563d31522cd492075e567e7e11b76a8207", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "c5f88916-431f-4bbe-b5cb-d5aefa1e7009", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-x86-linux.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-x86-linux.so"}], "coordinates": {"coordinate1": "libsigar-x86-linux.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "26e1db04d8fc8cdbbfe99038aee34df96b147f5ed23ebea380a46d3e5867260b", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "6f40a6b7-f6af-4f81-a495-0c85ba5e410b", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-x86-freebsd-5.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-x86-freebsd-5.so"}], "coordinates": {"coordinate1": "libsigar-x86-freebsd-5.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "d99934753c690afda0e4a20c1acd639697e67f42432932637bd4ce2eb201b824", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "fcaf21f2-d178-48a8-9afd-04da9ce45ec7", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-sparc-solaris.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-sparc-solaris.so"}], "coordinates": {"coordinate1": "libsigar-sparc-solaris.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "cace476abbffb3cefc2e94c35b52e0cf7dc89cf033d0b958c635eef8035f648e", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "827789ea-33ab-458e-940d-d6816562a816", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-amd64-solaris.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-amd64-solaris.so"}], "coordinates": {"coordinate1": "libsigar-amd64-solaris.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "a2ff2f8e078b44b98e4780c0dcdaae0b1d3ff3b98df6730316f889dd5b50acc8", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "085ab686-9568-44d1-bb3c-ab866c15d563", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-ppc64-linux.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-ppc64-linux.so"}], "coordinates": {"coordinate1": "libsigar-ppc64-linux.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "14f8f023bbc32240705e789428fdfe89166826dd930307507bb65f80a88e79c9", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "32d51813-4e9f-4fd7-8ffc-8fb5d4b16d53", "evidencePaths": [{"filePath": "libc.so.6.1", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libc.so.6.1"}], "coordinates": {"coordinate1": "libc.so", "coordinate2": null, "version": "6.1", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "ebb17624-0e27-4253-b946-468b1a0abc19", "evidencePaths": [{"filePath": "libsigar.zip#zip:libsigar-x86-solaris.so", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "libsigar.zip#zip:libsigar-x86-solaris.so"}], "coordinates": {"coordinate1": "libsigar-x86-solaris.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO"}, "sha1": null, "sha2": "7c726f95fa003f841dc0506eab7f8ce6679f0a2d33a15ef02ac47b064ade83ba", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}]