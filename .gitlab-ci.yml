include:
  - project: sca/ops-tools/gitlab-includes
    ref: 'release/1.0.x'
    file:
      - initial-script.yml
      - security.yml
      - build-push-image.yml
  - project: sca/sca-pipeline-includes
    ref: 'release/1.0.x'
    file:
      - stage-deployments.yml
  - .gitlab/ci/changelog.gitlab-ci.yml

variables:
  PROJECT_DEPLOYMENTS: agent-wrapper,agent-wrapper-dlq
  MAVEN_CLI_OPTS: "--batch-mode"
  MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  PROJECT_NAME: "${CI_PROJECT_NAME} :burrito:"
  TESTCONTAINERS_HUB_IMAGE_NAME_PREFIX: docker-ro.laputa.veracode.io/sca/ops-tools/gitlab-includes/
  JAVA_BUILD_IMAGE: docker-ro.laputa.veracode.io/sca/base-images/build:latest

cache:
  key: $CI_PROJECT_PATH
  paths:
    - target/
    # for .kube-deploys, the .m2/repository is required since those jobs can't connect to sourceclear maven repository
    - .m2/repository/
    - kubectl

.update-image:
  image: docker-ro.laputa.veracode.io/sca/sca-buildbox:openjdk-11
  before_script:
    - echo "Deploying to ${CI_ENVIRONMENT_NAME}"
  stage: deploy-stage
  dependencies: []
  when: manual
  variables:
    GIT_STRATEGY: none
    POD_IMAGE: ${DOCKER_PULL_HOST}/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:$CI_PIPELINE_ID
  script:
    - for deployment in $(echo $PROJECT_DEPLOYMENTS | sed "s/,/ /g");
      do
      kubectl -n ${CI_ENVIRONMENT_NAME} set image deployment/$deployment $deployment=${POD_IMAGE} --record=true;
      kubectl -n ${CI_ENVIRONMENT_NAME} rollout status deployment/$deployment;
      done
  only:
    - master

stages:
  - build
  - test
  - security
  - build-push-image
  - deploy-qa
  - deploy-stage
  - deploy-prod
  - nexus-release
  - changelog

build:
  image: $JAVA_BUILD_IMAGE
  stage: build
  tags:
    - sourceclear
  artifacts:
    expire_in: 6 hours
    paths:
      - clients/target/clients*.jar
      - service/target/service*.jar
      - Dockerfile
      - dockerfile-entrypoint.sh
      - bundletool-all/bundletool-all-*.jar
      - srcdot-*
  script:
    - mvn $MAVEN_CLI_OPTS clean package -DskipTests

test:
  image: $JAVA_BUILD_IMAGE
  stage: test
  tags:
    - sourceclear
  script:
    - mvn $MAVEN_CLI_OPTS test
  except:
    - master

sourceclear:
  image: $JAVA_BUILD_IMAGE

#allow use of rules otherwise a bug in gitlab triggers two pipelines - one MR pipeline and another branch/tag pipeline on commit push.
# See https://gitlab.com/gitlab-org/gitlab/-/issues/34756.
workflow:
  rules:
    - if: $CI_MERGE_REQUEST_ID
      when: never
    - when: always

# Add build-push-image-ecr job to build-push-image stage
build-push-image-ecr:
  extends: .build-push-image-ecr
  rules:
    - if: $CI_COMMIT_TAG
      when: always
      allow_failure: false
    - when: manual
      allow_failure: true

deploy-qa:
  image: docker-ro.laputa.veracode.io/sca/sca-buildbox:openjdk-11
  stage: deploy-qa
  environment:
    name: stage-152
  variables:
    GIT_STRATEGY: none
    POD_IMAGE: ${DOCKER_PULL_HOST}/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:$CI_PIPELINE_ID
  script:
    - for deployment in $(echo $PROJECT_DEPLOYMENTS | sed "s/,/ /g");
      do
      kubectl -n ${CI_ENVIRONMENT_NAME} set image deployment/$deployment $deployment=${POD_IMAGE} --record=true;
      kubectl -n ${CI_ENVIRONMENT_NAME} rollout status deployment/$deployment;
      done
    - 'curl -X POST -H "Content-Type: application/json" --data "{\"text\": \"$GITLAB_USER_NAME deployed pipeline <$CI_PIPELINE_URL|$CI_PIPELINE_ID> for $CI_PROJECT_NAME to $K8S_QA_NS\"}" $SLACK_SCA_DEV_WEBHOOK_URL'
  only:
    - branches

# Remove when networking with EKS runners is worked out.
default:
  tags:
    - docker-machine-master

deploy-prod:
  image: docker-ro.laputa.veracode.io/sca/sca-buildbox:openjdk-11
  stage: deploy-prod
  environment:
    name: prod
  variables:
    GIT_STRATEGY: none
    POD_IMAGE: ${DOCKER_PULL_HOST}/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:$CI_PIPELINE_ID
  script:
    - for deployment in $(echo $PROJECT_DEPLOYMENTS | sed "s/,/ /g");
      do
      kubectl -n ${KUBE_NAMESPACE_PROD} set image deployment/$deployment $deployment=${POD_IMAGE} --record=true;
      kubectl -n ${KUBE_NAMESPACE_PROD} rollout status deployment/$deployment;
      done
  only:
    - master
  when: manual
  allow_failure: false

maven-release:
  image: $JAVA_BUILD_IMAGE
  stage: nexus-release
  variables:
    MAVEN_OPTS: "-Dmaven.repo.local=.m2/repository"
  script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - 'which git || ( apt-get update -y && apt-get install git -y )'
    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | base64 --decode | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - '[[ -f /.dockerenv ]] && echo -e "Host *\n\tStrictHostKeyChecking no\n\tUserKnownHostsFile=/dev/null\n\n" > ~/.ssh/config'
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Hedwig"
    - git checkout -B "$CI_COMMIT_REF_NAME"
    - mvn $MAVEN_CLI_OPTS -DskipTests -Darguments=-DskipTests clean release:prepare release:perform
  only:
    - master
  when: on_success
  allow_failure: false
  artifacts:
    expire_in: 6 hours
    paths:
      - service/target/service*.jar

# Removing the contents of the archives-test directory in targert directory

# This directory contain libraries used for functional tests.

# They contain vulnerabilities, which are identified at static scan

veracode-static:
  before_script:
    - rm -rf service/target/
    # To verify contents
    - find . \( -type f -a -path '*/target/*.jar' -o -path '*/dist/*' \)
