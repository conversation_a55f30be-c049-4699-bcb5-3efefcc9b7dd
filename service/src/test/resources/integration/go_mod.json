[{"evidenceId": "d2c01630-90c9-4297-a5cb-0549f61bab2b", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/mattn/go-runewidth", "coordinate2": null, "version": "v0.0.4", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "d1cf3060-403f-4b19-bd57-551634b8bca2", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "golang.org/x/sys", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "5e06dd20ab57", "evidenceType": "COMMIT"}, {"evidenceId": "801bbcee-368d-4d4a-baab-9537e447e2da", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/simeji/jid", "coordinate2": null, "version": "v0.7.6", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "5826bfdc-5735-43ba-b8bf-1c767b435008", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/pkg/errors", "coordinate2": null, "version": "v0.8.0", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "dee4a41b-c25a-488d-a368-401e2c0ca865", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/bitly/go-simplejson", "coordinate2": null, "version": "v0.5.0", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "cfc7811a-3548-434a-ad52-4328598a20c8", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/google/go-github", "coordinate2": null, "version": "v17.0.0", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "110820dc-c73b-4772-bae9-f99150c0fb48", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/nsf/termbox-go", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "60ab7e3d12ed", "evidenceType": "COMMIT"}, {"evidenceId": "26c5415d-2a5a-43eb-976b-553a075eaa8f", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/mattn/go-isatty", "coordinate2": null, "version": "v0.0.4", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "12345774-c7dd-4f12-86a9-ad1cb66b1fe0", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/fatih/color", "coordinate2": null, "version": "v1.7.0", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "e43096e7-7e53-424b-b864-234e2b8a9818", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/google/go-querystring", "coordinate2": null, "version": "v1.0.0", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "de3f3481-781a-410a-8509-c8dc73224bdf", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/nwidger/jsoncolor", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "75a6de4340e5", "evidenceType": "COMMIT"}, {"evidenceId": "3a743106-0e9f-4d65-8e97-e6e05ed00662", "evidencePaths": [{"filePath": "go_mod.zip#zip:go.sum", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "go_mod.zip#zip:go.sum"}], "coordinates": {"coordinate1": "github.com/mattn/go-colorable", "coordinate2": null, "version": "v0.0.9", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}]