package com.sourceclear.agent.wrapper.service.queue

import com.github.kittinunf.result.Result
import com.sourceclear.agent.wrapper.scanconfig.client.Client
import com.sourceclear.agent.wrapper.scanconfig.client.ScanStatus
import com.sourceclear.agent.wrapper.scanconfig.client.ScanStatusRequest
import com.sourceclear.agent.wrapper.service.LOGGER
import com.sourceclear.agent.wrapper.service.config.SourceClearCommunication
import com.sourceclear.agent.wrapper.service.objects.ScanRequestMessage
import com.sourceclear.agent.wrapper.service.queue.unpackers.UNPACK_DELIMITER
import com.sourceclear.agent.wrapper.service.queue.unpackers.UNPACK_DIR_SUFFIX
import com.sourceclear.agent.wrapper.service.utils.CloseableTempIO
import com.sourceclear.agent.wrapper.service.utils.aesCipherWith
import com.sourceclear.api.data.evidence.Coordinates
import com.sourceclear.api.data.evidence.Evidence
import com.sourceclear.api.data.evidence.EvidenceType
import com.sourceclear.api.data.match.VeracodeScan
import com.srcclr.sdk.CoordinateType
import io.mockk.every
import io.mockk.mockkClass
import io.mockk.slot
import io.mockk.FunctionAnswer
import org.apache.commons.codec.binary.Hex
import org.apache.commons.lang3.StringUtils
import org.apache.commons.lang3.builder.ReflectionToStringBuilder
import org.apache.commons.lang3.builder.ToStringBuilder
import org.apache.commons.lang3.builder.ToStringStyle
import org.jetbrains.annotations.NotNull
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.context.annotation.Primary
import org.springframework.test.context.ContextConfiguration
import org.testcontainers.containers.localstack.LocalStackContainer
import org.testcontainers.containers.localstack.LocalStackContainer.Service.S3
import org.testcontainers.shaded.com.fasterxml.jackson.core.type.TypeReference
import org.testcontainers.shaded.com.fasterxml.jackson.databind.DeserializationFeature
import org.testcontainers.shaded.com.fasterxml.jackson.databind.ObjectMapper
import org.testcontainers.utility.DockerImageName
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.kms.KmsClient
import software.amazon.awssdk.services.s3.S3AsyncClient
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.CreateBucketRequest
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import software.amazon.awssdk.transfer.s3.S3TransferManager
import software.amazon.awssdk.transfer.s3.model.UploadDirectoryRequest
import java.io.File
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.time.Duration
import java.time.temporal.ChronoUnit
import java.util.Collections
import java.util.UUID
import java.util.concurrent.TimeoutException
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.crypto.Cipher
import javax.crypto.CipherInputStream
import kotlin.io.path.nameWithoutExtension
import kotlin.reflect.full.memberProperties
import kotlin.reflect.jvm.isAccessible
import kotlin.test.assertTrue
import kotlin.test.assertEquals


class EvidenceModuloId(@NotNull evidence: Evidence) {

  private val evidencePaths: Set<String> = evidence.evidencePaths.map { it.filePath }.toSet()

  private val coordinates: Coordinates? = evidence.coordinates

  private val sha1: String? = evidence.sha1

  private val sha2: String? = evidence.sha2

  private val bytecodeHash: String? = evidence.bytecodeHash

  private val commitHash: String? = evidence.commitHash

  private val evidenceType: EvidenceType? = evidence.evidenceType

  override fun equals(other: Any?): Boolean {
    if (this === other) return true
    if (javaClass != other?.javaClass) return false

    other as EvidenceModuloId

    if (evidencePaths != other.evidencePaths) return false
    if (coordinates != other.coordinates) return false
    if (sha1 != other.sha1) return false
    if (sha2 != other.sha2) return false
    if (bytecodeHash != other.bytecodeHash) return false
    if (commitHash != other.commitHash) return false
    if (evidenceType != other.evidenceType) return false

    return true
  }

  override fun hashCode(): Int {
    var result = evidencePaths.hashCode()
    result = 31 * result + (coordinates?.hashCode() ?: 0)
    result = 31 * result + (sha1?.hashCode() ?: 0)
    result = 31 * result + (sha2?.hashCode() ?: 0)
    result = 31 * result + (bytecodeHash?.hashCode() ?: 0)
    result = 31 * result + (commitHash?.hashCode() ?: 0)
    result = 31 * result + (evidenceType?.hashCode() ?: 0)
    return result
  }

  override fun toString(): String {
    return ReflectionToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE).toString()
  }

}

@ContextConfiguration(classes = [QueueHandlerIntegrationTest.QueueHandlerIntegrationTestConfig::class])
@SpringBootTest
class QueueHandlerIntegrationTest {

  init {
    localStack.start()

    val projectDir = Paths.get("").toAbsolutePath().toString()
    val bundletoolPath = Paths.get(projectDir, "../bundletool-all/bundletool-all-1.13.1.jar")
    System.setProperty("BUNDLETOOL_PATH", bundletoolPath.toFile().absolutePath)
  }

  companion object {

    private val BUCKET_NAME = "test-bucket"
    private val KEY = "27e08ca1edec290fccd8e69b29b7b5784c9de82924dfac2bb3bbfdcddb5e0843"
    private val IV = "f7d7a723a479d0ae32454eff472a0282"
    private val CIPHER = with(Hex()) {
      aesCipherWith(
          decode(ByteBuffer.wrap(KEY.toByteArray())),
          decode(ByteBuffer.wrap(IV.toByteArray())),
          Cipher.ENCRYPT_MODE)
    }

    val veracodeScanCaptor = slot<VeracodeScan>()

    val scanStatusCaptor = slot<ScanStatusRequest>()

    val localStack: LocalStackContainer = LocalStackContainer(DockerImageName.parse("localstack/localstack:2.2.0"))
        .withServices(S3)

    @JvmStatic
    fun possibilities(): List<Arguments> {
      return listOf(
          Arguments.of("binaries", true),
          Arguments.of("binaries", false),
          Arguments.of("scabinaries", true),
          Arguments.of("scabinaries", false))
    }

    /**
     * This will auto-discover test pairs (pack, json) in "integration" folder
     */
    @JvmStatic
    fun zipAndEvidencePairs(): List<Arguments> {
      val arguments = ArrayList<Arguments>()
      val builder = ToStringBuilder(arguments, ToStringStyle.MULTI_LINE_STYLE)

      val projectDir = Paths.get("").toAbsolutePath().toString()
      val integrationPath = Paths.get(projectDir, "/src/test/resources/integration")
      Files.walk(integrationPath)
        .filter { path -> Files.isRegularFile(path) }
        .filter { path -> "json" == path.toFile().extension }
        .forEach { path ->
          val pattern = path.fileName.nameWithoutExtension
          val integrationDir = integrationPath.toFile()
          val matched = integrationDir.listFiles { _, name -> name.startsWith(pattern) }

          var key = StringUtils.EMPTY
          var value = StringUtils.EMPTY
          for (x in matched!!) {
            if (x.name.endsWith("json")) value = x.name
            if (!x.name.endsWith("json")) key = x.name
            if (StringUtils.isNotEmpty(key) && StringUtils.isNotEmpty(value)) {
              builder.append(key, value)
              arguments.add(Arguments.of(key, value))
              key = StringUtils.EMPTY
              value = StringUtils.EMPTY
            }
          }
        }

      LOGGER.info("----- discovered the following testing pairs -----")
      LOGGER.info(builder.toString())
      return arguments
    }
  }

  @Configuration
  class QueueHandlerIntegrationTestConfig {

    @Primary
    @Bean
    fun amazonS3(): S3Client {
      return S3Client.builder()
        .endpointOverride(localStack.getEndpoint())
        .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(localStack.getAccessKey(), localStack.getSecretKey())))
        .region(Region.of(localStack.getRegion()))
        .build()
    }

    @Bean
    fun amazonKMS(): KmsClient {
      // never need it if you never use it
      return mockkClass(KmsClient::class)
    }

    @Bean
    fun s3AsyncClient(): S3AsyncClient {
      return S3AsyncClient.builder()
        .endpointOverride(localStack.getEndpoint())
        .credentialsProvider(StaticCredentialsProvider.create(AwsBasicCredentials.create(localStack.getAccessKey(), localStack.getSecretKey())))
        .region(Region.of(localStack.getRegion()))
        .build()
    }

    @Primary
    @Bean
    fun mockedSourceClearCommunication(): SourceClearCommunication {
      val sourceClearQueue: SourceClearCommunication = mockkClass(SourceClearCommunication::class)
      every {
        sourceClearQueue.veracodeScan(capture(veracodeScanCaptor))
      } returns Result.success("Doesn't matter")
      return sourceClearQueue
    }

    @Primary
    @Bean
    fun client(): Client {
      val scanConfigClient: Client = mockkClass(Client::class)
      every {
        scanConfigClient.sendScanStatus(capture(scanStatusCaptor))
      } returns Result.success("Doesn't matter")
      return scanConfigClient
    }
  }

  @Autowired
  private lateinit var amazonS3: S3Client

  @Autowired
  private lateinit var kmsClient: KmsClient

  @Autowired
  private lateinit var s3AsyncClient: S3AsyncClient

  @Autowired
  private lateinit var client: Client

  @Autowired
  private lateinit var sourceClearClient: SourceClearCommunication

  private var queueHandler: QueueHandler? = null

  private var queueHandlerWithDelayedUpload: QueueHandler? = null

  private fun queueHandler(): QueueHandler {
    if (queueHandler == null) {
      queueHandler = QueueHandler(amazonS3, kmsClient, client, sourceClearClient, Duration.ofDays(10L))
    }
    return queueHandler as QueueHandler
  }

  /*
   * This QueueHandler will process binaries as normal, but will experience a delay of 30 seconds
   *  while sending evidence to the evidence queue.
   */
  private fun queueHandlerWithDelayedUpload(): QueueHandler {
    if (queueHandlerWithDelayedUpload == null) {
      val answerWithDelay = FunctionAnswer { Thread.sleep(30000); Result.success("Doesn't matter")}
      val sourceClearClient: SourceClearCommunication = mockkClass(SourceClearCommunication::class)
      every {
        sourceClearClient.veracodeScan(capture(veracodeScanCaptor))
      }.answers(answerWithDelay)
      queueHandlerWithDelayedUpload = QueueHandler(amazonS3, kmsClient, client, sourceClearClient, Duration.ofDays(10L))
    }
    return queueHandlerWithDelayedUpload as QueueHandler
  }

  @BeforeEach
  fun setupEach() {
    // clear up the slot for the next test
    veracodeScanCaptor.clear()
    scanStatusCaptor.clear()
  }

  @ParameterizedTest
  @MethodSource("possibilities")
  fun `test possibilities for scanRequestListener and ensure similar behaviour`(binariesName: String, isArchive: Boolean) {
    val scanRequestMessage = simulateBinaries(
        setOf(Paths.get(QueueHandlerIntegrationTest::class.java.classLoader.getResource("example-python-pipenv.zip")!!.toURI()),
            Paths.get(QueueHandlerIntegrationTest::class.java.classLoader.getResource("example-javascript-yarn.zip")!!.toURI()),
            Paths.get(QueueHandlerIntegrationTest::class.java.classLoader.getResource("example-ruby.zip")!!.toURI())),
        binariesName = binariesName,
        isArchive = isArchive
    )
    queueHandler().scanRequestListener(scanRequestMessage, true, 1, Duration.of(30, ChronoUnit.MINUTES))
    val evidences = veracodeScanCaptor.captured.evidence.filterNotNull()
    val expectedCoordinateTypes = mutableSetOf(CoordinateType.GEM.name, CoordinateType.NPM.name, CoordinateType.PYPI.name)
    evidences.forEach { expectedCoordinateTypes.remove(it.coordinates.coordinateType.name) }
    assertTrue(expectedCoordinateTypes.isEmpty(), "CoordinateType: `$expectedCoordinateTypes` is not found")
    evidences.flatMap { it.evidencePaths }.forEach {
      val isValidPath = it.filePath.startsWith("example-python-pipenv.zip$UNPACK_DELIMITER")
          || it.filePath.startsWith("example-javascript-yarn.zip$UNPACK_DELIMITER")
          || it.filePath.startsWith("example-ruby.zip$UNPACK_DELIMITER")
      assertTrue(isValidPath)
    }
  }

  @Test
  fun `test removal of path duplicates`() {
    val scanRequestMessage = simulateBinaries(
        setOf(Paths.get(QueueHandlerIntegrationTest::class.java.classLoader.getResource("war-in-war.zip")!!.toURI())),
        binariesName = "binaries",
        isArchive = true
    )
    queueHandler().scanRequestListener(scanRequestMessage, true, 1, Duration.of(30, ChronoUnit.MINUTES))
    val evidenceSet = veracodeScanCaptor.captured.evidence.filterNotNull()
    assertTrue { evidenceSet.count() == 3 }
    evidenceSet.forEach {
      assertTrue { it.evidencePaths.filter { it.filePath.contains(UNPACK_DIR_SUFFIX) }.isEmpty() }
    }
    // expect 3 because 2 JAR types and 1 BYTECODE
    assertTrue(evidenceSet.map { it.evidencePaths }.map { it[0] }.filter { it.filePath.contains(".war#zip:WEB-INF/lib/commons-pool-1.5.4.jar") }.count() == 2)
    assertTrue(evidenceSet.filter { it.evidenceType == EvidenceType.JAR }.count() == 2)
    assertTrue(evidenceSet.filter { it.evidenceType == EvidenceType.BYTECODE }.count() == 1)

    val bytecodeEvidence = evidenceSet.first { it.evidenceType == EvidenceType.BYTECODE }
    assertTrue(bytecodeEvidence.evidencePaths.size == 2)


  }

  /**
   * This is a parameterized test that, given a package (zip, war, ear, or jar) in the integration subdirectory of
   * the test resources, and the JSON recording of the evidence, compares the scan result of the package with the
   * evidence in the recorded JSON. Please refer to the method "scan capture" for an automated way of generating
   * the evidence JSON from a package.
   */
  @ParameterizedTest
  @MethodSource("zipAndEvidencePairs")
  fun `test scan`(zipFile: String, evidenceJson: String) {
    val zipFilePath = "integration/$zipFile"
    val evidenceJsonPath = "integration/$evidenceJson"
    val expected = readEvidence(evidenceJsonPath)

    val scanRequestMessage = simulateBinaries(
        setOf(Paths.get(QueueHandlerIntegrationTest::class.java.classLoader.getResource(zipFilePath)!!.toURI())),
        binariesName = "binaries",
        isArchive = true
    )
    queueHandler().scanRequestListener(scanRequestMessage, true, 1, Duration.of(30, ChronoUnit.MINUTES))
    showTargetJson()

    val target = getResultEvidence()
    diffSet(expected, target)

    val normalizedExpected = normalizeEvidenceSetSimple(expected)
    val normalizedActual = normalizeEvidenceSetSimple(getResultEvidence())

    assertEquals(normalizedExpected, normalizedActual)
  }

  fun evidenceKey(evidence: Any): Triple<String?, String?, Set<String>> {
    val toString = evidence.toString()
    val bytecodeHash = Regex("bytecodeHash=(\\w+)?").find(toString)?.groupValues?.get(1)
    val evidenceType = Regex("evidenceType=(\\w+)").find(toString)?.groupValues?.get(1)
    val paths = Regex("evidencePaths=\\[(.*?)\\]").find(toString)?.groupValues?.get(1)
      ?.split(",")?.map { it.trim() }?.toSet() ?: emptySet()

    return Triple(bytecodeHash, evidenceType, paths)
  }

  fun normalizeEvidenceSetSimple(set: Set<Any>): Set<Triple<String?, String?, Set<String>>> {
    return set.map { evidenceKey(it) }
      .groupBy { it.first to it.second } // bytecodeHash + evidenceType
      .map { (_, group) ->
        val mergedPaths = group.flatMap { it.third }.toSet()
        Triple(group.first().first, group.first().second, mergedPaths)
      }.toSet()
  }

  @Test
  fun `should throw an error when the scan times out`() {
    val expectedExceptionMessage = "Timed out while scanning binaries - timeout: 1 second, scanId:"
    val expectedScanStatusInfo = "java.util.concurrent.TimeoutException: null"
    // Adding a big archive to ensure scan takes more than 1 SECOND
    val scanRequestMessage = simulateBinaries(
      setOf(Paths.get(QueueHandlerIntegrationTest::class.java.classLoader.getResource("big.zip")!!.toURI())),
      binariesName = "binaries",
      isArchive = true
    )
    val exception = assertThrows<TimeoutException> {
      queueHandler().scanRequestListener(scanRequestMessage, true, 1, Duration.of(1, ChronoUnit.SECONDS))
    }
    // Assert the error message is as expected
    exception.message?.let { assertTrue(it.contains(expectedExceptionMessage)) }
    // Assert the status is sent correctly
    val scanStatus = scanStatusCaptor.captured
    assertTrue { scanStatus.status == ScanStatus.FAILED }
    assertTrue { scanStatus.statusInfo.equals(expectedScanStatusInfo) }
  }

  @Test
  fun `should not throw an error if evidence is being uploaded`() {
    val scanRequestMessage = simulateBinaries(
      setOf(Paths.get(QueueHandlerIntegrationTest::class.java.classLoader.getResource("war-in-war.zip")!!.toURI())),
      binariesName = "binaries",
      isArchive = true
    )
    // Should not fail, even if the timeout is shorter than the delay experienced during uploading the evidence
    queueHandlerWithDelayedUpload().scanRequestListener(scanRequestMessage, true, 1, Duration.of(10, ChronoUnit.SECONDS))
    val evidenceSet = veracodeScanCaptor.captured.evidence.filterNotNull()
    assertTrue { evidenceSet.count() == 3 }
    evidenceSet.forEach {
      assertTrue { it.evidencePaths.filter { it.filePath.contains(UNPACK_DIR_SUFFIX) }.isEmpty() }
    }
    // expect 3 because 2 JAR types and 1 BYTECODE
    assertTrue(evidenceSet.map { it.evidencePaths }.map { it[0] }.filter { it.filePath.contains(".war#zip:WEB-INF/lib/commons-pool-1.5.4.jar") }.count() == 2)
    assertTrue(evidenceSet.filter { it.evidenceType == EvidenceType.JAR }.count() == 2)
    assertTrue(evidenceSet.filter { it.evidenceType == EvidenceType.BYTECODE }.count() == 1)

    val bytecodeEvidence = evidenceSet.first { it.evidenceType == EvidenceType.BYTECODE }
    assertTrue(bytecodeEvidence.evidencePaths.size == 2)
  }

  @Test
  fun `should scan v3 Lockfile`() {

    for(artifact in listOf("example-java-script-lock-fileV3.zip", "example-java-script-lock-fileV3-duplicate-locks.zip",
      "example-java-script-lock-fileV3-shrikwrap.zip", "example-javascript-backward-compatibilty.zip",
      "example-js-lockfile-no-version.zip")){
      val scanRequestMessage = simulateBinaries(
        setOf(
          Paths.get(
            QueueHandlerIntegrationTest::class.java.classLoader.getResource(artifact)!!
              .toURI()
          )
        ),
        binariesName = "binaries",
        isArchive = true
      )
      // Should not fail, even if the timeout is shorter than the delay experienced during uploading the evidence
      queueHandlerWithDelayedUpload().scanRequestListener(
        scanRequestMessage,
        true,
        1,
        Duration.of(10, ChronoUnit.SECONDS)
      )
      val evidenceSet = veracodeScanCaptor.captured.evidence.filterNotNull()
      // Assert for a particular coord presence
      assertTrue(evidenceSet.filter { it.coordinates.coordinate1.equals("socket.io-adapter") }.count() ==1)
    }
  }

  @Test
  fun `should scan dontnet depsjson`() {
    for (artifact in listOf(
      "example-dotnet-quickscan.zip"
    )) {
      val scanRequestMessage = simulateBinaries(
        setOf(
          Paths.get(
            QueueHandlerIntegrationTest::class.java.classLoader.getResource(artifact)!!
              .toURI()
          )
        ),
        binariesName = "binaries",
        isArchive = true
      )
      // Should not fail, even if the timeout is shorter than the delay experienced during uploading the evidence
      queueHandlerWithDelayedUpload().scanRequestListener(
        scanRequestMessage,
        true,
        1,
        Duration.of(10, ChronoUnit.SECONDS)
      )
      val evidenceSet = veracodeScanCaptor.captured.evidence.filterNotNull()

      // Assert for a particular coord presence
      assertTrue(evidenceSet.filter { it.coordinates.coordinate1.equals("gsf.security") }.count() == 1)
    }
  }

  /**
   * This method is for scanning a test package (zip, jar, war, or ear) in the integration-candidate subdirectory of the test
   * resources and outputs a JSON file containing the evidence in the same integration-candidate subdirectory. That is, it
   * "captures" the JSON of the scan result for use by the "test scan" parameterized test. The (package, json) pair if ready
   * for the final test case, simply move the pair to integration folder, it will be automatically tested in "test scan"
   */
  @Test
  fun `scan capture`() {
    val extSet = setOf("zip", "ear", "war", "jar", "apk", "aar", "aab", "tar")
    val projectDir = Paths.get("").toAbsolutePath().toString()
    val candidatePath = Paths.get(projectDir, "/src/test/resources/integration-candidate")

    Files.walk(candidatePath)
      .filter { path -> Files.isRegularFile(path) }
      .filter { path -> path.toFile().extension in extSet }
      .forEach { path ->
        val zipFile = path.toFile()
        val zipAbsPath = zipFile.absolutePath
        for (ext in extSet) {
          if (zipAbsPath.endsWith(ext)) {
            val jsonAbsPath = zipAbsPath.replace(".$ext", ".json")
            val jsonPath = File(jsonAbsPath).toPath()
            val scanRequestMessage = simulateBinaries(
              setOf(path),
              binariesName = "binaries",
              isArchive = true
            )
            queueHandler().scanRequestListener(scanRequestMessage, true, 1, Duration.of(30, ChronoUnit.MINUTES))
            val evidenceSet = veracodeScanCaptor.captured.evidence.filterNotNull()
            val objectMapper = ObjectMapper()
            val json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(evidenceSet)
            File(jsonPath.toAbsolutePath().toString()).writeText(json)
          }
        }
      }
  }

  @Test()
  fun `should mark scan as failed for unsupported zip file scan and be able to continue scanning`() {
    val scanRequestMessage = simulateBinaries(
      setOf(
        Paths.get(
          QueueHandlerIntegrationTest::class.java.classLoader.getResource("unsupportedCompressFilesInLZFSE.zip")!!
            .toURI()
        )
      ), binariesName = "binaries", isArchive = false
    )

    queueHandler().scanRequestListener(scanRequestMessage, true, 1, Duration.of(15, ChronoUnit.SECONDS))

    val scanStatus = scanStatusCaptor.captured
    assertTrue { scanStatus.status == ScanStatus.FAILED }

    `should scan v3 Lockfile`()
  }

  /* Utility Functions */

  private fun showTargetJson() {
    if (LOGGER.isTraceEnabled) {
      val evidenceSet = veracodeScanCaptor.captured.evidence.filterNotNull()
      val objectMapper = ObjectMapper()
      val json = objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(evidenceSet)

      LOGGER.trace("----- targetSet -----")
      LOGGER.trace(json)
    }
  }

  private fun diffSet(expected: Set<EvidenceModuloId>, target: Set<EvidenceModuloId>) {
    val builder1 = StringBuilder()
    for (x in target) {
      if (!expected.contains(x))
        builder1.append(x).append(Character.LINE_SEPARATOR)
    }
    if (builder1.isNotEmpty()) {
      LOGGER.debug("----- not in expectedSet -----")
      LOGGER.debug(builder1.toString())
    }

    val builder2 = StringBuilder()
    for (x in expected) {
      if (!target.contains(x))
        builder2.append(x).append(Character.LINE_SEPARATOR)
    }
    if (builder2.isNotEmpty()) {
      LOGGER.debug("----- not in targetSet -----")
      LOGGER.debug(builder2.toString())
    }
  }

  private fun readEvidence(path: String): Set<EvidenceModuloId> {
    val objectMapper = ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)

    val typeRef: TypeReference<Set<Evidence?>?> = object : TypeReference<Set<Evidence?>?>() {}
    val evidences: Set<Evidence?> = QueueHandlerIntegrationTest::class.java.classLoader.getResourceAsStream(path)!!.use {
      objectMapper.readValue(it, typeRef)
    }
    return evidences.filterNotNull()
        .map(::EvidenceModuloId)
        .toSet()
  }

  private fun getResultEvidence(): Set<EvidenceModuloId> {
    return veracodeScanCaptor.captured.evidence
        .filterNotNull()
        .map(::EvidenceModuloId)
        .toSet()
  }

  private fun simulateBinaries(files: Set<Path>, binariesName: String = "binaries", isArchive: Boolean = true): ScanRequestMessage {
    amazonS3.createBucket(CreateBucketRequest.builder().bucket(BUCKET_NAME).build())
    val keyName = if (isArchive) {
      archiveUpload(binariesName, files)
    } else {
      nonArchiveUpload(binariesName, files)
    }
    return ScanRequestMessage(
        binaryPath = keyName,
        bucketName = BUCKET_NAME,
        encryptIv = IV,
        encryptKey = KEY,
        encryptedKey = "something",
        // don't bother with the following
        accountId = 1L,
        appId = 1L,
        appVerId = 1L,
        scanId = UUID.randomUUID().toString(),
        scanConfigId = "something",
        env = "something",
        appName = "App name",
        analysisId = 1L,
        jobId = 1L,
        sandboxId = 1L,
        sandbox = false,
        analysisUnitId = 1L,
        scaEnabled = true,
        teamIds = Collections.singletonList("1001".toLong()),
        policyLegacyId = 1L,
        policyVersion = 0,
        scanName = "test",
        submittedBy = 123,
        submitterName = "test-user"
    )
  }

  private fun archiveUpload(binariesName: String, files: Set<Path>): String {
    // TODO: make the structure more complex here
    val filename = "$binariesName.zip.aes"
    val keyName = generateBinaryPath(filename)
    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val binariesPath = tempDir.resolve("$binariesName.zip")
      ZipOutputStream(FileOutputStream(binariesPath.toFile())).use { zipOutputStream ->
        zipOutputStream.putNextEntry(ZipEntry("binaries/"))
        files.forEach {
          zipOutputStream.putNextEntry(ZipEntry("binaries/%s".format(it.fileName)))
          Files.copy(it, zipOutputStream)
          zipOutputStream.closeEntry()
        }
      }
      val cipherInputStream = CipherInputStream(binariesPath.toFile().inputStream(), CIPHER)
      val binariesAesPath = tempDir.resolve(filename)
      Files.copy(cipherInputStream, binariesAesPath)
      amazonS3.putObject(PutObjectRequest.builder().bucket(BUCKET_NAME).key(keyName).build(), binariesAesPath)
    }
    return keyName
  }

  private fun nonArchiveUpload(filename: String, files: Set<Path>): String {
    // TODO: make the structure more complex here
    val keyName = generateBinaryPath(filename)
    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      files.forEach {
        val cipherInputStream = CipherInputStream(it.toFile().inputStream(), CIPHER)
        val binariesAesPath = tempDir.resolve(it.fileName.toString() + ".aes")
        Files.copy(cipherInputStream, binariesAesPath)
      }

      val uploadDirectoryRequest  = UploadDirectoryRequest.builder()
                                      .bucket(BUCKET_NAME)
                                      .source(tempDir)
                                      .s3Prefix(keyName)
                                      .build()
      val completedDirectoryUpload = S3TransferManager.builder().s3Client(s3AsyncClient).build().uploadDirectory(uploadDirectoryRequest).completionFuture().join()
      completedDirectoryUpload.failedTransfers().forEach{
        LOGGER.error("Object {} failed to upload", it.toString())
      }
      assertTrue(completedDirectoryUpload.failedTransfers().isEmpty(), "Failed to upload object(s) to s3 bucket")
    }
    return keyName
  }

  private fun generateBinaryPath(name: String): String =
      "/%s/%s/%s".format(
          UUID.randomUUID().toString().replace("-", "_"),
          UUID.randomUUID().toString().replace("-", "_"),
          name)
}
