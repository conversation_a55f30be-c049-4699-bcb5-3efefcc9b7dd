[{"evidenceId": "32f16725-fcb9-4552-8ba0-37a3fc6a5dae", "evidencePaths": [{"filePath": "liblog.so", "lineNumber": null, "dependencyPath": [{"@id": 1, "coordinate1": "libucs-credential.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}]}], "coordinates": {"@id": 2, "coordinate1": "liblog.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "3a295dae-1c1d-485a-911f-7ef7a93daa76", "evidencePaths": [{"filePath": "libc.so", "lineNumber": null, "dependencyPath": [1]}], "coordinates": {"@id": 3, "coordinate1": "libc.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "2bce5f0f-a02e-4a96-b0fd-04e81137a0be", "evidencePaths": [{"filePath": "arm64-v8a.zip#zip:arm64-v8a/libucs-credential.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 4, "coordinate1": "libucs-credential.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "2a2bb2c70b9ba6cdeaa0259e4d30fcebc4ca732953251e78eb9ae9a4b5edc074", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "ff17f35b-e35d-4b0f-a05f-1ddcade8d0ea", "evidencePaths": [{"filePath": "libm.so", "lineNumber": null, "dependencyPath": [1]}], "coordinates": {"@id": 5, "coordinate1": "libm.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "d206709e-f20b-43e6-94a7-7838f6846cb9", "evidencePaths": [{"filePath": "arm64-v8a.zip#zip:arm64-v8a/libfilterengine.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 6, "coordinate1": "libfilterengine.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "0c891e7d617f1a0e136abae8a2f189d5ed109329c8865a39a799d0a64120d4cc", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "496d151a-f1ac-4265-890d-fdbd19b4676f", "evidencePaths": [{"filePath": "arm64-v8a.zip#zip:arm64-v8a/libTransform.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 7, "coordinate1": "libTransform.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "51207a2d6de86b0c996b22b915f150999c081bf817524a2c948e0fe3252e6be1", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "d85707c9-46c1-432a-807a-2914eafce279", "evidencePaths": [{"filePath": "arm64-v8a.zip#zip:arm64-v8a/libsqlcipher.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 8, "coordinate1": "libsqlcipher.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "51d7c616464249ad03dd0504dd60b38e5cc6db6534d036f7a8173092e3d00cfc", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "3823d475-4016-44d3-8719-ea52e02df1d2", "evidencePaths": [{"filePath": "libdl.so", "lineNumber": null, "dependencyPath": [1]}], "coordinates": {"@id": 9, "coordinate1": "libdl.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "6931feef-7835-4bb9-ac91-44887ce3dafa", "evidencePaths": [{"filePath": "libandroid.so", "lineNumber": null, "dependencyPath": [{"@id": 10, "coordinate1": "libfilterengine.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}]}], "coordinates": {"@id": 11, "coordinate1": "libandroid.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "658a4e1e-1c3f-40bd-b13f-40ef90131874", "evidencePaths": [{"filePath": "libOpenSLES.so", "lineNumber": null, "dependencyPath": [10]}], "coordinates": {"@id": 12, "coordinate1": "libOpenSLES.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}]