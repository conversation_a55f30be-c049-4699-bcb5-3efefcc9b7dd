[{"evidenceId": "ddef991a-3089-45a7-b3b2-158e33ddc432", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 22, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L22"}], "coordinates": {"coordinate1": "github.com/nsf/termbox-go", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "abe82ce5fb7a42fbd6784a5ceb71aff977e09ed8", "evidenceType": "COMMIT"}, {"evidenceId": "61545521-6218-44da-9212-1d86c2d0dfa4", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 26, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L26"}], "coordinates": {"coordinate1": "github.com/pkg/errors", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "248dadf4e9068a0b3e79f02ed0a610d935de5302", "evidenceType": "COMMIT"}, {"evidenceId": "381d54b8-84dc-439f-a8d2-19a524b1491b", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 6, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L6"}], "coordinates": {"coordinate1": "github.com/fatih/color", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "42c364ba490082e4815b5222728711b3440603eb", "evidenceType": "COMMIT"}, {"evidenceId": "d8270d56-760f-4d80-a40b-9512159d5bef", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 35, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L35"}], "coordinates": {"coordinate1": "golang.org/x/sys", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "d75a52659825e75fff6158388dddc6a5b04f9ba5", "evidenceType": "COMMIT"}, {"evidenceId": "75c70214-eb6c-4f3a-8b39-4e48ffe3f552", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 12, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L12"}], "coordinates": {"coordinate1": "github.com/google/go-querystring", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "53e6ce116135b80d037921a7fdd5138cf32d7a8a", "evidenceType": "COMMIT"}, {"evidenceId": "6b5be68d-1ba6-44d7-9672-858a70f57d47", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 20, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L20"}], "coordinates": {"coordinate1": "github.com/mattn/go-runewidth", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "737072b4e32b7a5018b4a7125da8d12de90e8045", "evidenceType": "COMMIT"}, {"evidenceId": "a745ceb0-21bf-4560-9afd-c84ac574cd18", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 30, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L30"}], "coordinates": {"coordinate1": "github.com/square/go-jose", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "7f0dd807d3f3d73bb536898cb7980ddf638ce69a", "evidenceType": "COMMIT"}, {"evidenceId": "730fd009-303e-4d03-be38-f0567da47af0", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 16, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L16"}], "coordinates": {"coordinate1": "github.com/mattn/go-colorable", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "d228849504861217f796da67fae4f6e347643f15", "evidenceType": "COMMIT"}, {"evidenceId": "d7f45ed3-ca52-4524-b025-61a080a50bdf", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 8, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L8"}], "coordinates": {"coordinate1": "github.com/google/go-github", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "173908142398084e14d7f14c39924f0004d43679", "evidenceType": "COMMIT"}, {"evidenceId": "4bc23b42-26e5-4b7c-b759-0518d7bb1c4c", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 28, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L28"}], "coordinates": {"coordinate1": "github.com/simeji/jid", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "1fe54ef34a09fab74f38ae40dcca6e1f0974d297", "evidenceType": "COMMIT"}, {"evidenceId": "c0554682-aa1b-4149-aaa4-87510920ddef", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 4, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L4"}], "coordinates": {"coordinate1": "github.com/bitly/go-simplejson", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "aabad6e819789e569bd6aabf444c935aa9ba1e44", "evidenceType": "COMMIT"}, {"evidenceId": "26871bbe-ae81-4b29-b42a-aa7ad6122a78", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 24, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L24"}], "coordinates": {"coordinate1": "github.com/nwidger/jsoncolor", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "0192e84d44af834c3a90c8a17bf670483b91ad5a", "evidenceType": "COMMIT"}, {"evidenceId": "7cfe2f69-7b33-4d61-8454-70921d2db3da", "evidencePaths": [{"filePath": "glide_lock.zip#zip:glide.lock", "lineNumber": 18, "dependencyPath": [], "filePathWithLineNumber": "glide_lock.zip#zip:glide.lock#L18"}], "coordinates": {"coordinate1": "github.com/mattn/go-isatty", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "30a891c33c7cde7b02a981314b4228ec99380cca", "evidenceType": "COMMIT"}]