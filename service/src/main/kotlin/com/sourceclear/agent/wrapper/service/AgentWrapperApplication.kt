package com.sourceclear.agent.wrapper.service

import com.sourceclear.agent.wrapper.service.scan.UploadScanAttributes
import com.sourceclear.agent.wrapper.service.utils.Constants
import org.slf4j.LoggerFactory
import org.springframework.boot.SpringApplication
import org.springframework.boot.autoconfigure.SpringBootApplication

@SpringBootApplication
class AgentWrapperApplication

val LOGGER = LoggerFactory.getLogger(AgentWrapperApplication::class.java)

fun main(args: Array<String>) {
  val ctx = SpringApplication.run(AgentWrapperApplication::class.java, *args)
  val env = ctx.environment
  val props = listOf(
      "sqs.queue",
      "scanconfig.url",
      "scanconfig.port",
      "srcclr.ishttp",
      "srcclr.url",
      "srcclr.queue",
      "srcclr.bucket",
      "role.arn",
      Constants.BUNDLETOOL_PATH_SPRING_PROP
  ).map { "$it = ${env.getProperty(it)}" }

  System.setProperty(Constants.BUNDLETOOL_PATH_SYSTEM_PROP, env.getProperty(Constants.BUNDLETOOL_PATH_SPRING_PROP))

  val attrs = UploadScanAttributes.staticAttributes.map { (k, v) ->
    val vStr = if (v.toString().isBlank()) "\"\"" else v.toString()
    "$k = $vStr"
  }

  val output =
      listOf("\n============================= Configuration Properties =============================") +
          props +
          listOf("================================== Scan Attributes =================================") +
          attrs +
          listOf("====================================================================================")

  LOGGER.info(output.joinToString("\n"))
}
