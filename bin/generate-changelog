#!/usr/bin/env ruby

require 'date'
require 'json'
require 'yaml'
require 'net/http'
require 'shellwords'

Abort = Class.new(StandardError)
def fail_with message
  raise Abort, "\e[31merror\e[0m #{message}"
end

# Simple Markdown DSL
class MD
  attr_accessor :document

  def initialize
    @document = ''
  end

  def markdown &block
    instance_eval &block
    self
  end

  def nl
    @document << "\n"
  end

  (1..6).each do |i|
    define_method "h#{i}" do |text|
      @document << "#{'#' * i} #{text}\n"
    end
  end

  def ul text
    @document << "- #{text}\n"
  end
end

jar = Dir['service/target/service-*.jar'].first
fail_with 'Unable to find service jar in service/target' if !jar

version = jar.match(/service-([0-9]+(.[0-9]+)+)/)&.captures&.first
fail_with 'Unable to grab the version from the latest jar' if !version

puts "Creating changelog for version #{version}"

unreleased = File.join 'changelogs', 'unreleased'

ChangelogEntry = Struct.new(:title, :merge_request, :ticket, :author, :type)
members = ChangelogEntry.members
unreleased_yml = Dir.entries(unreleased)
                    .select { |entry| entry != '.' && entry != '..' }
                    .select { |entry| entry.end_with?('.yml') || entry.end_with?('.yaml') }

all_entries = unreleased_yml.map do |entry|
  path = File.join unreleased, entry
  text = File.read path
  h = YAML.load text

  # DRY-er way of creating a struct from a hash
  vals = members.map { |m| h[m.to_s] }
  ChangelogEntry.new *vals
end

types = all_entries.group_by { |entry| entry.type }

release_header = "#{version} (#{Date.today.strftime})"
items = []

output = MD.new.markdown do
  h1 release_header
  nl

  types.each do |type, entries|
    size = entries.size
    num_text = if size > 1 then "#{size} changes" else "#{size} change" end

    h2 "#{type.capitalize} (#{num_text})"
    nl

    entries.each do |entry|
      item = "[#{entry.ticket}] #{entry.title}. #{entry.merge_request} (#{entry.author})"
      ul item
      items << item
    end
    nl

  end

  nl
end

puts 'Prepending to CHANGELOG.md:'
puts output.document

lines = File.readlines 'CHANGELOG.md'
lines.shift 1 # Remove the notes at the top of the file

# w+ overwrites the existing file
File.open 'CHANGELOG.md', 'w+' do |f|
  f.write "**Note:** This file is automatically generated. Please see the [changelog readme](changelogs/README.md) for instructions on adding your own entry. \n"
  f.write output.document
  lines.each { |l| f.write "#{l}" }
end

unreleased_yml.each do |f|
  `git rm changelogs/unreleased/#{f}`
end

`git add CHANGELOG.md`
`git commit -m '[ci skip] Update changelog for #{version}'`
`git push origin master`

data = {
  text: ":fire: #{ENV['PROJECT_NAME']} #{version} release :fire:",
  channel: ENV['SLACK_CHANNEL'],
  username: 'srcbot',
  icon_url: 'https://hubot.github.com/assets/images/layout/<EMAIL>',
  attachments: [{
    title: release_header,
    text: items.map { |item| '- ' + item }.join("\n") + "\nFor more :fire: details see :eyes: :point_right: #{ENV['CI_PROJECT_URL']}/blob/master/CHANGELOG.md :point_left:"
  }]
}

url = ENV['SLACK_WEBHOOK_URL']
if url
  `curl -vL #{Shellwords.escape(url)} -H 'Content-Type: application/json' -d #{Shellwords.escape(data.to_json)}`
else
  puts 'Skipping slack notification.'
end
# vim: ft=ruby
