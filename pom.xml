<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.sourceclear.agent.wrapper</groupId>
  <artifactId>wrapper-parent</artifactId>
  <version>1.0.125-SNAPSHOT</version>
  <packaging>pom</packaging>

  <name>Agent Wrapper</name>
  <description>Agent wrapper for the sourceclear engines</description>

  <parent>
    <groupId>org.springframework.boot</groupId>
    <artifactId>spring-boot-starter-parent</artifactId>
    <version>3.2.9</version>
    <relativePath /> <!-- lookup parent from repository -->
  </parent>

  <modules>
    <module>service</module>
    <module>clients</module>
    <module>cli</module>
  </modules>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <kotlin.version>1.9.0</kotlin.version>
    <spring-cloud-aws.version>3.0.1</spring-cloud-aws.version>
    <kotlin.compiler.incremental>true</kotlin.compiler.incremental>
    <engines.version>2.26.115</engines.version>
    <!-- Force to engines to use 4.5.38, FIPS api-java is not included in utils 2.11.98 / engines 2.26.29 -->
    <api-java.version>4.5.78</api-java.version>
    <jjwt.version>0.10.5</jjwt.version>
    <agora.version>19.1.1</agora.version>
    <result.version>2.2.0</result.version>
    <guava.version>32.1.1-jre</guava.version>
    <logback.version>1.4.14</logback.version>
    <testcontainers.version>1.15.3</testcontainers.version>
    <awaitility.version>4.2.0</awaitility.version>
    <aws-sdk.version>2.20.162</aws-sdk.version>
    <jackson.version>2.19.0</jackson.version>
  </properties>

  <scm>
    <url>https://gitlab.laputa.veracode.io/sca/agent-wrapper</url>
    <connection>scm:git:*****************************:sca/agent-wrapper.git</connection>
    <developerConnection>scm:git:*****************************:sca/agent-wrapper.git</developerConnection>
    <tag>HEAD</tag>
  </scm>

  <distributionManagement>
    <repository>
      <uniqueVersion>false</uniqueVersion>
      <id>laputaReleases</id>
      <name>Laputa Releases</name>
      <url>https://maven.laputa.veracode.io/api/object/releases</url>
      <layout>default</layout>
    </repository>
  </distributionManagement>

  <repositories>
    <repository>
      <id>mvnrepository.com</id>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <url>https://repo1.maven.org/maven2/</url>
      <layout>default</layout>
    </repository>
    <repository>
      <id>maven.apache.org</id>
      <releases>
        <enabled>true</enabled>
      </releases>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
      <url>https://repo.maven.apache.org/maven2</url>
    </repository>
    <repository>
      <id>laputaReleases</id>
      <url>https://maven.laputa.veracode.io/api/object/releases</url>
      <snapshots>
        <enabled>false</enabled>
      </snapshots>
    </repository>
<!--    Keep this part for branch build if need snapshots of dependency project, such as engines, api-java etc-->
    <repository>
      <id>laputaSnapshots</id>
      <url>https://maven.laputa.veracode.io/api/object/snapshots</url>
      <releases>
        <enabled>true</enabled>
      </releases>
    </repository>
    <repository>
      <id>jitpack.io</id>
      <url>https://jitpack.io</url>
    </repository>
    <repository>
      <id>googleMaven</id>
      <url>https://maven.google.com</url>
    </repository>
  </repositories>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-stdlib-jdk8</artifactId>
        <version>${kotlin.version}</version>
      </dependency>

      <dependency>
        <groupId>jaxen</groupId>
        <artifactId>jaxen</artifactId>
        <version>1.1.6</version>
      </dependency>

      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-test</artifactId>
        <version>${kotlin.version}</version>
      </dependency>

      <dependency>
        <groupId>com.github.kittinunf.result</groupId>
        <artifactId>result</artifactId>
        <version>${result.version}</version>
      </dependency>

      <dependency>
        <groupId>com.sourceclear.api</groupId>
        <artifactId>data</artifactId>
        <version>${api-java.version}</version>
      </dependency>

      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>

      <!-- Fix vuln CVE-2021-44228-->
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api</artifactId>
        <version>2.17.0</version>
      </dependency>

      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-to-slf4j</artifactId>
        <version>2.17.0</version>
      </dependency>

      <!-- Fix vuln CVE-2023-6378-->
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${logback.version}</version>
      </dependency>

      <dependency>
        <groupId>io.github.hakky54</groupId>
        <artifactId>sslcontext-kickstart</artifactId>
        <version>6.7.0</version>
      </dependency>

      <dependency>
        <groupId>io.github.hakky54</groupId>
        <artifactId>sslcontext-kickstart-for-apache4</artifactId>
        <version>6.7.0</version>
      </dependency>

      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${logback.version}</version>
      </dependency>

      <!-- Remove the CVE-2022-1471 vuln from snakeyaml <= 1.33. Should be fine to upgrade to 2.0 with Spring Boot >= 3.1.0 -->
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>2.0</version>
      </dependency>

      <!-- Fix vuln CVE-2023-4759-->
      <dependency>
        <groupId>org.eclipse.jgit</groupId>
        <artifactId>org.eclipse.jgit</artifactId>
        <version>6.6.1.202309021850-r</version>
      </dependency>

      <dependency>
        <groupId>com.fasterxml.jackson</groupId>
        <artifactId>jackson-bom</artifactId>
        <version>${jackson.version}</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
