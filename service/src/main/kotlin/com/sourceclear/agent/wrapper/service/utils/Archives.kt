package com.sourceclear.agent.wrapper.service.utils

import com.sourceclear.agent.wrapper.service.queue.unpackers.Unpacker
import com.sourceclear.engine.component.collectors.npm.internal.NpmFileAnalyser.NPMFilePatterns.PACKAGE_FILENAME
import com.sourceclear.engine.component.collectors.npm.internal.NpmFileAnalyser.NPMFilePatterns.PACKAGE_LOCK_FILENAME
import com.sourceclear.engine.component.collectors.npm.internal.NpmFileAnalyser.NPMFilePatterns.SHRINKWRAP_FILENAME
import org.apache.commons.compress.archivers.ArchiveEntry
import org.apache.commons.compress.archivers.ArchiveInputStream
import org.apache.commons.compress.archivers.tar.TarArchiveInputStream
import org.apache.commons.compress.archivers.zip.ZipArchiveInputStream
import org.apache.commons.compress.compressors.gzip.GzipCompressorInputStream
import org.apache.commons.io.FilenameUtils
import org.apache.commons.lang.StringUtils
import org.slf4j.LoggerFactory
import java.io.File
import java.io.IOException
import java.io.InputStream
import java.nio.file.Files
import java.nio.file.Path
import java.nio.file.Paths
import java.nio.file.StandardCopyOption
import java.util.*

private val LOGGER = LoggerFactory.getLogger(Unpacker::class.java)


fun unZip(file: Path, target: Path) {
  unZip(file, target, "")
}

fun unZip(file: Path, target: Path, contentFilenameExtension: String) {
  val lowerCaseContentFilenameExtension = contentFilenameExtension.lowercase()
  var currentEntryName: String? = null
  try {
    Files.newInputStream(file).use {
      ZipArchiveInputStream(it, "UTF8", false, true).each { entry, stream ->
        currentEntryName = entry.name
        if (entry.isDirectory || StringUtils.isEmpty(entry.name)) {
          return@each
        }
        val filename = Paths.get(entry.name)
        if (!isAcceptableFilename(filename, target)) {
          LOGGER.warn("Filename unacceptable. Skipping {}", filename)
          return@each
        }
        if (!filename.toString().lowercase().endsWith(lowerCaseContentFilenameExtension)) {
          // Skip silently when the file does not have the desired extension.
          return@each
        }
        if (filename.parent != null) {
          Files.createDirectories(target.resolve(filename.parent))
        }
        val targetFile = target.resolve(filename)
        LOGGER.trace("Archived file name {}", targetFile)

        Files.copy(stream, targetFile, StandardCopyOption.REPLACE_EXISTING)
      }
    }
  } catch (e: IOException) {
    LOGGER.warn("Error unzipping file: ${currentEntryName}. Cause: ${e.message}", e)
    throw e
  }
}


/**
 * JS files can be present in a jar file.
 * This unzips only the js package files because handling node_modules and prioritization
 * will turn this function into a mess.
 * No need to do the above mentioned because we defer the responsibility to the quickscan engine .
 *
 * @param file the jar/zip file that you want to unpack
 * @param target directory path to unzip the js files to
 */
fun unZipJsInJar(file: Path, target: Path) {
  Files.newInputStream(file).use {
    ZipArchiveInputStream(it).each { entry, stream ->
      if (entry.isDirectory) {
        return@each
      }
      val fileName = Paths.get(entry.name)
      if (!isAcceptableFilename(fileName, target)) {
        LOGGER.warn("Filename unacceptable. Skipping {}", fileName)
        return@each
      }
      if (!isJsEvidenceFile(fileName)) {
        return@each
      }
      if (fileName.parent != null) {
        Files.createDirectories(target.resolve(fileName.parent))
      }
      val targetFile = target.resolve(fileName)
      LOGGER.trace("Archived file name {}", targetFile)
      Files.copy(stream, targetFile, StandardCopyOption.REPLACE_EXISTING)
    }
  }
}


/**
 * Check if file is something we use in JS quickscan collectors.
 * Do not bother with recognising node_modules directory.
 * Much simpler if we just extract the package files, including the ones inside node_modules
 * and let collector handle the differentiation.
 *
 * @param fileName file that you want to check
 * @return if file is a js trace
 */
fun isJsEvidenceFile(fileName: Path): Boolean =
    setOf(PACKAGE_LOCK_FILENAME.value, SHRINKWRAP_FILENAME.value, PACKAGE_FILENAME.value)
        .any { fileName.endsWith(it) }

fun unTarGz(file: Path, target: Path) = unTarGz(Files.newInputStream(file), target)

fun unTarGz(inputStream: InputStream, target: Path) {
  GzipCompressorInputStream(inputStream).use {
    TarArchiveInputStream(it).each { entry, stream ->
      if (entry.isDirectory) {
        return@each
      }
      val filename = Paths.get(entry.name)
      if (!isAcceptableFilename(filename, target)) {
        LOGGER.warn("Filename unacceptable. Skipping {}", filename)
        return@each
      }
      if (filename.parent != null) {
        Files.createDirectories(target.resolve(filename.parent))
      }
      val file = target.resolve(filename)
      Files.copy(stream, file, StandardCopyOption.REPLACE_EXISTING)
    }
  }
}

fun unTar(file: Path, target: Path) = unTar(Files.newInputStream(file), target)

fun unTar(inputStream: InputStream, target: Path) {
  TarArchiveInputStream(inputStream).each { entry, stream ->
    // SCA-14678 issue fix by skipping files with '.' name
    if (entry.isDirectory || entry.name.equals(".")) {
      return@each
    }
    val filename = Paths.get(entry.name).normalize()
    if (!isAcceptableFilename(filename, target)) {
      LOGGER.warn("Filename unacceptable. Skipping {}", filename)
      return@each
    }
    if (filename.parent != null) {
      Files.createDirectories(target.resolve(filename.parent))
    }
    val file = target.resolve(filename)
    Files.copy(stream, file, StandardCopyOption.REPLACE_EXISTING)
  }
}

/**
 * Search a folder recursively or a file if matches the given set of extension
 *
 * @param file the folder or file to be searched
 * @param extensions a set of extension
 */
fun containsExtensions(file: File, extensions: Set<String>): Boolean {
  if (file.isDirectory) {
    val files = file.listFiles()
    for (f in files!!) {
      if(containsExtensions(f, extensions)) return true
    }
  } else {
    if (extensions.contains(FilenameUtils.getExtension(file.name).lowercase(Locale.getDefault()))) {
      return true
    }
  }
  return false
}

private inline fun <reified T : ArchiveEntry> ArchiveInputStream<T>
    .each(block: (entry: T, stream: ArchiveInputStream<T>) -> Unit) {
  var entry: T?
  this.use {
    while (run {
        entry = this.nextEntry
        entry
      } != null) {
      block(entry!!, this)
    }
  }
}

fun isAcceptableFilename(entryPath: Path, target: Path) =
    // most fs have 255 byte restriction for filenames
    entryPath.fileName.toString().toByteArray().size < 255
        // check that the path will only be added to the target directory
        // to mitigate zip :
        // * https://snyk.io/research/zip-slip-vulnerability
        // * https://help.semmle.com/wiki/pages/viewpage.action?pageId=29393405
        && target.resolve(entryPath).normalize().startsWith(target)
