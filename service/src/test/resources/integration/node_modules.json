[{"evidenceId": "0ce49d9a-1be4-4235-bc42-de65859dd83b", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/methods", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/methods"}], "coordinates": {"coordinate1": "methods", "coordinate2": null, "version": "0.1.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "518aa9d4-0387-4947-b557-7876a66f5b06", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/cookie-signature", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/cookie-signature"}], "coordinates": {"coordinate1": "cookie-signature", "coordinate2": null, "version": "1.0.3", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "610b06dd-46af-47a1-8abe-d4f6a617c910", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/buffer-crc32", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/buffer-crc32"}], "coordinates": {"coordinate1": "buffer-crc32", "coordinate2": null, "version": "0.2.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "6a238e4b-7f3b-4263-920b-90625b7a0230", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/range-parser", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/range-parser"}], "coordinates": {"coordinate1": "range-parser", "coordinate2": null, "version": "1.0.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "8f929512-3df1-4560-b348-c18f374d42e5", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/path-to-regexp", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/path-to-regexp"}], "coordinates": {"coordinate1": "path-to-regexp", "coordinate2": null, "version": "0.1.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "ce6214fe-60a3-42f3-991c-a3635bbad900", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/angular", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/angular"}], "coordinates": {"coordinate1": "angular", "coordinate2": null, "version": "1.3.19", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "d8957f53-094b-4ccd-ad39-2fb0817ec45a", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/send/node_modules/debug", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/send/node_modules/debug"}], "coordinates": {"coordinate1": "debug", "coordinate2": null, "version": "0.8.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "3224cfd3-a7f5-440e-935d-8b2e72997d68", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/negotiator", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/negotiator"}], "coordinates": {"coordinate1": "negotiator", "coordinate2": null, "version": "0.4.9", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "582ae9f0-b46d-4fec-ab68-456d7254d851", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/type-is", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/type-is"}], "coordinates": {"coordinate1": "type-is", "coordinate2": null, "version": "1.1.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "ce132091-3d35-4d94-889f-2476e718941c", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/utils-merge", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/utils-merge"}], "coordinates": {"coordinate1": "utils-merge", "coordinate2": null, "version": "1.0.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "b482dbd9-841f-477a-9eed-895d29908ce4", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/jquery", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/jquery"}], "coordinates": {"coordinate1": "j<PERSON>y", "coordinate2": null, "version": "3.0.0-alpha1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "9cba0422-e274-4b27-bb22-8f521ee6d15f", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/mime", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/mime"}], "coordinates": {"coordinate1": "mime", "coordinate2": null, "version": "1.2.11", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "99f801d6-5b39-49ba-912c-d12704f282a4", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/debug", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/debug"}], "coordinates": {"coordinate1": "debug", "coordinate2": null, "version": "0.8.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "bae42641-878c-467b-b406-164e68f87819", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/merge-descriptors", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/merge-descriptors"}], "coordinates": {"coordinate1": "merge-descriptors", "coordinate2": null, "version": "0.0.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "76db71e3-fdec-446e-8c32-87dcf0092ebc", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/escape-html", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/escape-html"}], "coordinates": {"coordinate1": "escape-html", "coordinate2": null, "version": "1.0.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "644ad071-f7f2-403c-b178-65e9edc7bad1", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/accepts", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/accepts"}], "coordinates": {"coordinate1": "accepts", "coordinate2": null, "version": "1.0.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "ce0508bb-7fe5-4efb-a0d5-dec48508fe13", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/fresh", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/fresh"}], "coordinates": {"coordinate1": "fresh", "coordinate2": null, "version": "0.2.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "59be54cc-95e5-4622-ac0f-91c4836867fb", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/serve-static", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/serve-static"}], "coordinates": {"coordinate1": "serve-static", "coordinate2": null, "version": "1.1.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "77c2a0fe-9c69-4344-b797-f6e0032bbaf6", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/parseurl", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/parseurl"}], "coordinates": {"coordinate1": "parseurl", "coordinate2": null, "version": "1.0.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "16788709-2144-4d64-8c2e-7d1e179b47aa", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/express", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/express"}], "coordinates": {"coordinate1": "express", "coordinate2": null, "version": "4.1.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "92876e37-f3e5-463a-ac01-6189aa34ba40", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/coffee-script", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/coffee-script"}], "coordinates": {"coordinate1": "coffee-script", "coordinate2": null, "version": "1.6.3", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "66a8297b-f5f3-4ee0-9c1e-3fdc69b12991", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/cookie", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/cookie"}], "coordinates": {"coordinate1": "cookie", "coordinate2": null, "version": "0.1.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "435fe3bd-6c6f-4ae3-9413-84a65558044c", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/qs", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/qs"}], "coordinates": {"coordinate1": "qs", "coordinate2": null, "version": "0.6.6", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "d66d02eb-12eb-4f58-9840-d8e8da6e0ab0", "evidencePaths": [{"filePath": "node_modules.zip#zip:node_modules/send", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "node_modules.zip#zip:node_modules/send"}], "coordinates": {"coordinate1": "send", "coordinate2": null, "version": "0.3.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}]