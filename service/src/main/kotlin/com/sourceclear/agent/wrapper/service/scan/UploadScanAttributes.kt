package com.sourceclear.agent.wrapper.service.scan

import com.sourceclear.engine.common.ScanDirectives
import com.sourceclear.engine.component.ComponentEngineBuilder

class UploadScanAttributes {
  companion object {
    // attributes that don't change because of Sqs message
    val staticAttributes = mapOf(
        // we need dependency graphs in the upload scans for SBOM support (PARTY-1299)
        Pair(ScanDirectives.NO_DEPENDENCY_GRAPH, false),
        Pair(ComponentEngineBuilder.FAT_JAR, true),
        Pair(ComponentEngineBuilder.FAT_AAR, true),
        Pair(ComponentEngineBuilder.NPM_SCOPE, "production"),
        Pair(ComponentEngineBuilder.NODE_MODULES, true),
        // don't ignore any dirs when scanning for DLLs, except node_modules. The node_modules exception is to avoid
        // matching compiled NPM DLLs such as edge-cs.dll with NUGET libraries.
        Pair(ComponentEngineBuilder.IGNORE_DLL_DIRS, "node_modules"),
        // don't ignore any dirs when scanning for JARs
        Pair(ComponentEngineBuilder.IGNORE_JAR_DIRS, ""),
        <PERSON>ir(ComponentEngineBuilder.IGNORE_AAR_DIRS, ""),
        Pair(ComponentEngineBuilder.BOWER_COMPONENTS, true),
        // set version/properties scan for apk to default
        Pair(ComponentEngineBuilder.APK_PROPERTY, true),
        // set the pip file scope to default, we don't need to scan dev dependencies
        Pair(ComponentEngineBuilder.PIPFILE_LOCK_SCOPE, "default")
    )

    // attributes that change because of Sqs message
    fun dynamicAttributes(receiveCount: Int = 1) = mapOf(
        // this attribute's upper bound is 3 in engines.
        // since receiveCount should always be 1 or more, the initial recursion depth would be 3
        Pair(ComponentEngineBuilder.FAT_JAR_MAX_RECURSION_DEPTH, 4 - receiveCount),
        Pair(ComponentEngineBuilder.FAT_AAR_MAX_RECURSION_DEPTH, 4 - receiveCount)
    )
  }
}
