[{"evidenceId": "2129cd2a-0928-40fd-b3f6-77d982c75507", "evidencePaths": [{"filePath": "liblog.so", "lineNumber": null, "dependencyPath": [{"@id": 1, "coordinate1": "libucs-credential.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}]}], "coordinates": {"@id": 2, "coordinate1": "liblog.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "8e8516fc-b9ac-4504-8ba0-1c0cccd3c22d", "evidencePaths": [{"filePath": "libm.so", "lineNumber": null, "dependencyPath": [1]}], "coordinates": {"@id": 3, "coordinate1": "libm.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "05fa5648-3823-499a-84c2-e786ab748fe2", "evidencePaths": [{"filePath": "libOpenSLES.so", "lineNumber": null, "dependencyPath": [{"@id": 4, "coordinate1": "libfilterengine.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}]}], "coordinates": {"@id": 5, "coordinate1": "libOpenSLES.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "774c4d92-e9b7-442f-9d00-052d206883e9", "evidencePaths": [{"filePath": "x86_64.zip#zip:x86_64/libsqlcipher.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 6, "coordinate1": "libsqlcipher.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "151adc11093fc7f3eb213bed934287fb2e5c4869a3e176a965494e0705f84bea", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "3136ede8-f168-4c01-adc6-1fd4d6e1b0ac", "evidencePaths": [{"filePath": "libdl.so", "lineNumber": null, "dependencyPath": [1]}], "coordinates": {"@id": 7, "coordinate1": "libdl.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "3b291d7c-c46a-4c9e-8ec2-45a61f099df8", "evidencePaths": [{"filePath": "libc.so", "lineNumber": null, "dependencyPath": [1]}], "coordinates": {"@id": 8, "coordinate1": "libc.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "f98a9810-c913-4ef7-9caf-84972ed32040", "evidencePaths": [{"filePath": "libandroid.so", "lineNumber": null, "dependencyPath": [4]}], "coordinates": {"@id": 9, "coordinate1": "libandroid.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "f43f872e-86e8-48d0-b71c-23ac6ae15603", "evidencePaths": [{"filePath": "x86_64.zip#zip:x86_64/libucs-credential.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 10, "coordinate1": "libucs-credential.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "c3180bf189d3573943945ab7524fa94fde0ffdc1b27b8f61260439ce0d6662aa", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "726c3fd3-2cd5-4f29-9cfd-cf82e6fc5c2a", "evidencePaths": [{"filePath": "x86_64.zip#zip:x86_64/libfilterengine.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 11, "coordinate1": "libfilterengine.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "cd51c5f1ab5e6f51b8da69fef826ea48701029a11c5f6ae0ebd028bf03ea3c7c", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}]