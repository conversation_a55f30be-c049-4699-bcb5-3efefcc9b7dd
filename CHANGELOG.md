**Note:** This file is automatically generated. Please see the [changelog readme](changelogs/README.md) for instructions on adding your own entry. 
# 1.0.123 (2025-05-14)

## Fixed (1 change)

- [SSCA-11017] Fix parsing of pyproject.toml with dotted keys. sca/engines!691 (<PERSON>)


# 1.0.122 (2025-04-25)

## Added (1 change)

- [SFP-69] Add poetry.lock scanning. sca/engines!678 (<PERSON>)


# 1.0.121 (2024-09-09)

## Added (1 change)

- [SSCA-589] Add submitterName (userName) to the scanRequest in upload scan. sca/agent-wrapper! (<PERSON><PERSON>)


# 1.0.120 (2024-09-04)

## Added (1 change)

- [ENG-48681] Update engines to fix yarn 4 plus scans. sca/agent-wrapper!239 (<PERSON>)


# 1.0.119 (2024-07-23)

## Fixed (2 changes)

- [ENG-48206] fix circular dependency in NPM v3 parser. sca/agent-wrapper!237 (<PERSON>)
- [SCA-19630] Agent-wrapper breaks if upgrade from engines 2.26.70 to 2.26.80. sca/agent-wrapper!238 (<PERSON>)


# 1.0.118 (2024-07-16)

## Fixed (1 change)

- [SCA-19117] change error to warn. sca/agent-wrapper!236 (Sahar Amini)


# 1.0.117 (2024-07-15)

## Fixed (1 change)

- [SCA-19117] Add logger to know which file causes Truncated ZIP archive. sca/agent-wrapper!235 (Sahar Amini)


# 1.0.116 (2024-05-15)

## Fixed (1 change)

- [SCA-18901] Fixing NPM issue for V3 lockfiles without project version. sca/engines!589 (Ahmed Elmallah)


# 1.0.115 (2024-05-09)

## Fixed (1 change)

- [SCA-18764] Upgrading engines version to fix NPM issues. sca/agent-wrapper!232 (Ahmed ElMallah)


# 1.0.114 (2024-04-11)

## Added (1 change)

- [SCA-17928] Add submittedBy (userId), scanName, policyVersion to the scanRequest in upload scan. sca/agent-wrapper!231 (Sahar Amini)


# 1.0.113 (2024-02-26)

## Changed (1 change)

- [SCA-16557] Add PolicyId to the scanRequest in upload scan - Agent-wrapper. sca/agent-wrapper!228 (Pratik Lanjewar)


# 1.0.112 (2024-01-23)

## Changed (2 changes)

- [SCA-16434] Modified scan request message adding team ids. sca/agent-wrapper!227 (Anuradha Jayakody)
- [SCA-16289] Changes to support ZIP/RBV files. sca/agent-wrapper!226 (Anuradha Jayakody)


# 1.0.111 (2023-12-13)

## Security (1 change)

- [SCA-16147] Fix high vulns like CVE-2023-46589. sca/agent-wrapper!224 (Seung Wook Kim)


# 1.0.110 (2023-11-28)


# 1.0.109 (2023-11-27)

## Fixed (7 changes)

- [SCA-15074] Mark scan as failed on unzip failure. sca/agent-wrapper!217 (Seung Wook Kim)
- [SCA-14326] grace handling in the case when un-packer failed to process detecting file type. sca/agent-wrapper!197 (Sam Zhou)
- [SCA-14657] Added tar file unpacker. sca/agent-wrapper!201 (Anuradha Jayakody)
- [SCA-14678] Workaround for directory paths starts with dot names. sca/agent-wrapper!207 (Anuradha Jayakody)
- [SCA-14984]  Fix pojects.assests.json scan when using SCOPE.Runtime. sca/engines!485 (sraghavan)
- [SCA-15653] CVE-2023-44487 fix. sca/agent-wrapper!217 (Seung Wook Kim)


# 1.0.108 (2023-11-27)

## Changed (1 change)

- [SCA-15824] Modified scan request message. sca/agent-wrapper!218 (Anuradha Jayakody)


# 1.0.107 (2023-09-29)

## Fix (1 change)

- [SCA-15075] Fix maven release job. sca/agent-wrapper!213 (Seung Wook Kim)

## Fixed (1 change)

- [SCA-14678] Workaround for directory paths starts with dot names. sca/agent-wrapper!207 (Anuradha Jayakody)


# 1.0.106 (2023-09-26)

## Deprecated (1 change)

- [SCA-14949] Fix changelog job in our CI pipeline. sca/agent-wrapper!209 (Seung Wook Kim)

## Bug_fix (1 change)

- [SCA-14984]  Fix pojects.assests.json scan when using SCOPE.Runtime. sca/engines!485 (sraghavan)


# 1.0.105 (2023-09-06)


# 1.0.104 (2023-08-22)


# 1.0.103 (2023-08-08)

## Fixed (1 change)

- [SCA-14657] Added tar file unpacker. sca/agent-wrapper!201 (Anuradha Jayakody)


# 1.0.102 (2023-08-08)

## Security (1 change)

- [SCA-13166] Upgrade to Spring Boot 3 and add ability to run agent-wrapper locally. sca/agent-wrapper!200 (Seung Wook Kim)


# 1.0.101 (2023-07-24)

## Fixed (1 change)

- [SCA-14326] grace handling in the case when un-packer failed to process detecting file type. sca/agent-wrapper!197 (Sam Zhou)


# 1.0.100 (2023-07-20)

## Feature (1 change)

- [SCA-14245] DotNetQuick scan collector implementation. sca/engines!476 (Srinivasan Raghavan)


# 1.0.99 (2023-06-20)

## Fixed (1 change)

- [SCA-14016] V3 semver implementation and update the graph resolution. sca/engines!475 (Srinivasan Raghavan)


# 1.0.98 (2023-06-07)

## Other (1 change)

- [SCA-11952] Upgrade to java 17. sca/agent-wrapper!187 (Seung Wook Kim)

## Fixed (2 changes)

- [SCA-13786] Fix tag pipeline to publish to ECR. sca/agent-wrapper!186 (Seung Wook Kim)
- [SCA-13970] Fix maven-release and changelog jobs in the release pipeline run. sca/agent-wrapper!189 (Seung Wook Kim)


# 1.0.97 (2023-01-31)

## Fixed (1 change)

- [SCA-12684] Parsing unsupported SO sometimes causes NPE. sca/agent-wrapper!182 (Sam Zhou)


# 1.0.96 (2023-01-31)

## Fixed (1 change)

- [SCA-12669] Update to new Engines version with checking on apk/aab folders. sca/agent-wrapper!181 (Haiyan Huang)


# 1.0.95 (2022-12-15)


# 1.0.94 (2022-12-14)

## Fixed (1 change)

- [SCA-11660] Add support for extracting/unpacking APK/AAR; Bundle build - AAB support; Nested Android packages. sca/agent-wrappe!177 (Sam Zhou)


# 1.0.93 (2022-11-18)


# 1.0.92 (2022-10-18)


# 1.0.91 (2022-08-30)


# 1.0.90 (2022-08-19)

## Fixed (1 change)

- [FED-1319] Force FIPS mode in AWS client in agent wrapper. sca/agent-wrappe!162 (Srinivasan Raghavan)


# 1.0.89 (2022-06-02)

## Fixed (1 change)

- [SCAXF-2587] Revert MR 158. sca/agent-wrapper!160 ()


# 1.0.88 (2022-05-10)

## Fixed (1 change)

- [ENG-13186] Added Nuget unpacker. sca/agent-wrapper!159 ()


# 1.0.87 (2022-05-09)

## Fixed (1 change)

- [SCAXF-2587] Agent-wrapper failed to send large amount of scan evidences to S3. sca/agent-wrapper!158 (Sam Zhou)


# 1.0.86 (2022-05-02)

## Fixed (2 changes)

- [] SCARR-3030.  ()
- [SCAXF-2572] Fix agent wrapper timeout thread issue. sca/agent-wrapper!156 (Sam Zhou)


# 1.0.85 (2022-03-22)

## Fixed (2 changes)

- [ENG-9462] Add default pipfile scope for upload and scans. https://gitlab.laputa.veracode.io/sca/agent-wrapper/-/merge_requests/153 (Anuradha Jayakody)
- [SCARR-2766] Set dependency graph collection to true. sourceclear/agent-wrapper!152 (Deepak Rao)


# 1.0.84 (2022-02-15)


# 1.0.76 (2022-01-27)

## Security (1 change)

- [SCARR-2636] Security Vulnerability fixes.  ()


# 1.0.83 (2021-12-20)

## Fixed (1 change)

- [SCAMA-2229] upgrade log4j library to 2.17.0. sca/agent-wrapper!148 (Sahar Amini)


# 1.0.82 (2021-12-17)


# 1.0.81 (2021-12-13)

## Fixed (1 change)

- [SCARR-2494] Fix for CVE-44228 - log4j library. sca/agent-wrapper!142 (Deepak Rao)


# 1.0.80 (2021-11-24)

## Fixed (1 change)

- [ENG-4599] Build DLL evidence even when Srcdot fails. sca/engines!397 ()


# 1.0.79 (2021-11-23)

## Fixed (1 change)

- [SCARR-2426] Automatically download Alpine srcdot. sca/gamma!125 ()


# 1.0.78 (2021-11-19)


# 1.0.77 (2021-11-17)

## Fixed (2 changes)

- [ENG-5163] Limit unpack exceptions to InvalidPathException, ZipException, and EOFException only. sca/agent-wrapper!137 ()
- [SGA-2453] Fixed nondetermistic failure in integration testing. sca/agent-wrapper!131 ()


# 1.0.76 (2021-11-04)

## Fixed (2 changes)

- [ENG-4777] Catch any recursive unzipping exceptions. sca/agent-wrapper!132 ()
- [SCASF-692] throw the timeout exception for scans to be picked up by dlq. sca/agent-wrapper!116 (Deepak Rao)

## Security (2 changes)

- [SGA-2665] Resolve SCA vulnerabilities in agent-wrapper. sca/agent-wrapper!126 (Ming)
- [SGA-2770] Resolve CVE-2021-33037 in agent-wrapper. sca/agent-wrapper!127,sca/agent-wrapper!128 (Ming)


# 1.0.75 (2021-07-07)

## Security (1 change)

- [SGA-2489] Resolve SCA vulnerabilities in agent-wrapper. sca/agent-wrapper!119 (Ming)


# 1.0.74 (2021-06-02)

## Fixed (1 change)

- [SGA-1211] Avoid searching for DLLs in node_modules. sca/agent-wrapper!117 ()


# 1.0.73 (2021-05-19)

## Fixed (1 change)

- [SCASF-692] Update scan timeout from 3h to 5m. sca/agent-wrapper!115 (Deepak Rao)


# 1.0.72 (2021-04-07)

## Fixed (1 change)

- [SGA-838] Generalize QueueHandler.normalizePath() to handle recursive unpacking paths. sca/agent-wrapper!111 ()


# 1.0.71 (2021-03-31)


# 1.0.70 (2021-03-30)

## Fixed (2 changes)

- [SGA-1041] Limit evidence merging in QueueHandler to only for Fat Jar evidence types. sca/agent-wrapper!109 ()
- [sga-900] Process dlq using a high memory instance. sca/agent-wrapper!104, sca/agent-wrapper!108 (Deepak Rao)


# 1.0.69 (2021-03-23)

## Fixed (1 change)

- [sga-838] Replace all periods (.) before .war#zip or #ear.zip prefixes. sca/agent-wrapper!106 (Jibrail Idris)


# 1.0.68 (2021-03-22)

## Fixed (1 change)

- [SGA-957] Fix dependency versions to vendored versions. sca/engines!358 ()


# 1.0.67 (2021-03-18)

## Fixed (1 change)

- [sga-930] Check if a parent exists for a file in WAR. sca/agent-wrapper!105 (Deepak Rao)


# 1.0.66 (2021-03-03)

## Fixed (2 changes)

- [sga-838] Remove duplicate evidence paths when unpacking WAR or EAR. sca/agent-wrapper!103 (Jibrail Idris)
- [SGA-875] Skip unpacking on InvalidPathException in recursive unpacking. sca/agent-wrapper!102 ()


# 1.0.65 (2021-02-22)

## Fixed (1 change)

- [SGA-826] Scan Go modules vendor directory structure when vendor/modules.txt is missing. sca/engines!353,sca/engines!354 ()


# 1.0.64 (2021-02-15)

## Fixed (1 change)

- [SGA-826] Remove incorrect inclusion based on repo hostname. sourceclear/engines!353 ()


# 1.0.63 (2021-02-10)

## Added (1 change)

- [SGA-826] Made go modules quickscan filter out dependencies not in vendor/modules.txt. sca/engines!350 ()


# 1.0.62 (2021-01-27)

## Fixed (1 change)

- [SGA-795] Add security stage. sca/agent-wrapper!97 (Ming)


# 1.0.61 (2021-01-08)

## Performance (1 change)

- [SGA-567] Reduce memory to 2/3 of allocated memory. sca/agent-wrapper!96 (cyou)


# 1.0.60 (2020-12-17)

## Fixed (1 change)

- [SGA-431] Fixing the NPM version override of the dependencies in package-lock.json. sca/engines!339 (Vimali Raaj)


# 1.0.59 (2020-11-18)

## Security (1 change)

- [No-Ticket] Mitigate zip slip. sca/agent-wrapper!94 (cyou)


# 1.0.58 (2020-09-30)


# 1.0.57 (2020-09-30)

## Fixed (1 change)

- [SGA-263] Ignore impossibly long filenames. sca/agent-wrapper!90 (cyou)


# 1.0.56 (2020-09-28)

## Added (1 change)

- [SGA-114] Send queue message instead of http request. sca/agent-wrapper!79 (cyou)


# 1.0.55 (2020-09-22)

## Changed (1 change)

- [No-Ticket] Close S3 Stream ASAP. sca/agent-wrapper!88 (cyou)


# 1.0.54 (2020-09-17)

## Added (1 change)

- [sga-143] Add scan timeout. sca/agent-wrapper!85 (cyou)


# 1.0.53 (2020-09-10)

## Performance (1 change)

- [No-Ticket] Add decrementing fat jar depth on retries. sca/agent-wrapper!83 && sca/engines!298 (cyou)


# 1.0.52 (2020-09-07)

## Fixed (1 change)

- [SCAA-2354] Enabled prioritization of YarnDotLockQuickscanCollector over NodeModulesQuickscanCollector. sca/engines!299 ( )


# 1.0.51 (2020-09-07)

## Performance (1 change)

- [sga-115] Reduce heap memory to prevent OOMKill. sca/agent-wrapper!82 (cyou)


# 1.0.50 (2020-08-28)

## Fixed (2 changes)

- [SGA-123] Check for seen coordinates to make graph to evidence conversion efficient. sca/utils!94 (Andrew Santosa)
- [SGA-131] Use the line numbers we collected from parsing the individual gem specs. sca/engines!294 (Jason Yeo)


# 1.0.49 (2020-08-24)

## Added (1 change)

- [sga-43] Add dead letter queue to incoming queue. sca/agent-wrapper!77 && sca/agent-wrapper!78 (cyou)


# 1.0.48 (2020-07-29)

## Fixed (1 change)

- [SCAA-2457] Fix OOM issue by retiring "relativizeAllFilePaths". sca/agent-wrapper!76 (cyou)


# 1.0.47 (2020-07-16)

## Fixed (1 change)

- [SCAA-2388] Delete unpacked archived files. sca/agent-wrapper!74 (cyou)


# 1.0.45 (2020-07-09)

## Fixed (1 change)

- [SCAA-2388] Allow JsInJar unpacker to fail. sca/agent-wrapper!72 (cyou)


# 1.0.44 (2020-06-17)


# 1.0.43 (2020-06-17)


# 1.0.42 (2020-06-17)

## Fixed (1 change)

- [SCAA-2350] Change how paths are determined for non-archived files and scan path.. sca/agent-wrapper!69 (cyou)


# 1.0.41 (2020-06-10)

## Fixed (1 change)

- [scaa-2361] Fix no file found exception. sca/agent-wrapper!70 (cyou)


# 1.0.40 (2020-06-01)

## Fixed (1 change)

- [SCAA-1947] Create bucket folder structure for unarchived files. sca/agent-wrapper!66 (cyou)


# 1.0.40 (2020-05-28)

## Fixed (1 change)

- [SCAA-1947] fix extension recognition for unarchived files.. sca/agent-wrapper!63 (cyou)


# 1.0.40 (2020-05-27)

## Fixed (2 changes)

- [SCAA-1947] Agent-Wrapper now reads un-archived files from s3 bucket. sca/agent-wrapper!60 (cyou)
- [SCAA-2137] Don't resolve dependency graphs in upload scans and thereby fix file path issues.. sca/agent-wrapper!61 (Jason Yeo)


# 1.0.40 (2020-05-19)

## Fixed (1 change)

- [scaa-2314] Agent-Wrapper now unpacks JS package files in Jars and scans them. sca/agent-wrapper!58 (cyou)


# 1.0.40 (2020-05-12)

## Fixed (1 change)

- [SCAA-2258] Allow reading fat jar files with prepended script. sca/libBytecode!10 (Andrew Santosa)


# 1.0.38 (2020-05-05)

## Fixed (1 change)

- [SCAA-2268] Prioritize lock file quick scanning over node_modules quick scanning. sca/engines!251 (Andrew Santosa)


# 1.0.37 (2020-04-13)

## Fixed (1 change)

- [SCAA-2237] Add a flag to skip computing of bytecode hash for fat jar scans. sca/engines!243 (Deepak Rao)


# 1.0.36 (2020-04-07)

## Added (1 change)

- [SCAA-2153] Expose a Prometheus endpoint to measure CPU, throughput, etc.. sca/agent-wrapper!52 (Deepak Rao)


# 1.0.35 (2020-03-02)

## Fixed (1 change)

- [SCAA-2071] Enables multiple paths per evidence (SCAA-2071). sourceclear/engines!218 (Andrew Santosa)


# 1.0.34 (2020-02-26)

## Changed (1 change)

- [SCAA-2006] Jar Scans no longer skip any folders. https://git.ci.srcclr.io/sourceclear/engines/merge_requests/214 && https://gitlab.laputa.veracode.io/sca/agent-wrapper/merge_requests/50 (Chen TianYou)


# 1.0.33 (2020-02-25)

## Fixed (1 change)

- [SCAA-2030] Append Jar paths when recursing into an inner Jar in fat jar analysis. sourceclear/engines!212 (Andrew Santosa)


# 1.0.32 (2020-02-24)

## Fixed (2 changes)

- [SCAA-2009] Fix PHP Composerlock OOM. 44 (Chen TianYou)
- [SCAA-2010] Improve scanning speed for DLLs. 48 (Chen TianYou)


# 1.0.30-SNAPSHOT (2020-02-06)

## Fixed (1 change)

- [SCAA-2011] Handle version suffix in library name in GoModulesNativeCollector and GoModulesQuickscanCollector. sourceclear/engines!187 (Andrew Santosa)


# 1.0.29 (2020-01-28)


# 1.0.28 (2020-01-20)

## Added (1 change)

- [SCAA-1847] Support unpack of rbv files for veracode ruby binary scanning. sca/agent-wrapper!40 (Deepak Rao)


# 1.0.27-SNAPSHOT (2020-01-14)

## Fixed (1 change)

- [SCA-9984] Add a foreach to scan all binaries in the S3 path. sca/agent-wrapper!36 (Jason Yeo)

## Added (1 change)

- [SCAA-1847] Add more logs to debug ruby binary scanning. sca/agent-wrapper!39 (Deepak Rao)


# 1.0.26 (2019-12-20)

## Fixed (2 changes)

- [SCAA-1853] Recursive Go modules quickscan. sourceclear/engines!174 (Andrew Santosa)
- [SCAA-1865] Replaced engines feature flags with environment variables. sca/agent-wrapper!37 (Andrew Santosa)


# 1.0.25-SNAPSHOT (2019-12-11)

## Added (1 change)

- [SCAA-10] Support Scopes for Quick Scan Engine JS. sourceclear/engines!164 (Andrew Santosa)


# 1.0.24-SNAPSHOT (2019-12-03)

## Added (1 change)

- [SCAA-1701] Parse lockfile and implement collection (for Go modules). sourceclear/engines!161 (Andrew Santosa)


# 1.0.23 (2019-11-15)

## Fixed (2 changes)

- [SCAA-1805] Utils incorrectly handles evidence type for folders containing class files and its names ends with .jar. sca/agent-wrapper!30 (Spencer)
- [SCAA-1808] Engines causes OOM when parsing package.json with empty dependecies. sca/agent-wrapper!30 (Spencer)


# 1.0.9 (2019-11-12)

## Fixed (1 change)

- [SCAR-964] Dedupe evidence with the same sha2 by combining their evidence paths. sourceclear/api-java!52 (Hendy Chua)


# 1.0.9 (2019-11-07)

## Security (1 change)

- [SCAA-1643] Use AWS assume role to access S3 buckets. 28 (spencer)


# 1.0.9 (2019-10-22)

## Added (2 changes)

- [SCAA-1726] Increase pod memory to 15GB. agent-wrapper!26 (Jason Yeo)
- [SCAA-1726] Increase pod memory to 15GB. agent-wrapper!26 (Jason Yeo)



# 1.0.9 (2019-10-18)

## Added (2 changes)

- [SCAF-2115] Prefix java bytecode evidence with jar file name. agent-wrapper!22 (Hao Xiao)
- [SCAA-1726] Stop ignoring dirs when scanning for DLLs. agent-wrapper!24 (Jason Yeo)

