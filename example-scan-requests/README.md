# Scan Request Examples

This directory stores example messages that are sent to the scan request sqs queue.

## The Agora message format

Here's an example of the message that is sent to the queue:

```
{
  "header": {
    "id": "c5cc0bcd-0a63-4f7c-aa31-0967b138d202",
    "send_time": 1551726120952,
    "send_timestamp": "2019-03-04T19:02:00.952Z",
    "receive_time": 1551726120953,
    "topic_name": "",
    "originating_application_id": "sca-scanconfig-service",
    "microservice_id": "sca-scanconfig-service",
    "environment": "",
    "security_context": {}
  },
  "principal": null,
  "context_id": null,
  "metadata": {},
  "content_type": "JSON",
  "content": "eyJzY2FuX2NvbmZpZ19pZCI6IjUyMTJhNzNhLTQwNjMtNGNhNi05NjVhLWM3YmRjYWI3NTQ4NiIsIm1lc3NhZ2UiOiJ7XCJlbmNyeXB0X2l2XCI6XCJhYmNkZWZncWFhcVwiLFwiYWNjb3VudF9pZFwiOjEzMzA0LFwiZW5jcnlwdF9rZXlcIjpcInh5emFiY2FhYWFlaVwiLFwiYmluYXJ5X3BhdGhcIjpcI
  "actor": {
    "name": "SYSTEM",
    "guid": "",
    "legacy_id": "",
    "type": "SYSTEM",
    "internal": true,
    "organization_guid": "",
    "organization_name": ""
  },
  "action": null,
  "resource": null,
  "audit": false
}
```

This complex message is mandated by the agora messaging library. As of writing
this, I have no idea what most of the fields do and how the encryption is done.
All I know and I'm interested in is the `content` field in this JSON message.

## Wrapped Message

The content field currently is base64 encoded representation of a JSON object
that looks like this:

```
{
  "scan_config_id": "5212a73a-4063-4ca6-965a-c7bdcab75486",
  "message": "{\"encrypt_iv\":\"abcdefgqaaq\",\"account_id\":13304,\"encrypt_key\":\"xyzabcaaaaei\",\"binary_path\":\"acc-13304\\/app100040\\/5212a73a-4063-4ca6-965a-c7bdcab75486\\/binaries\\/\",\"encrypted_key\":\"aaaaaaa\",\"bucket_name\":\"
}
```

Oddly enough, the `message` field is a JSON string that wraps around another JSON object.

## Scan Request Message

And this JSON object is the new Scan Request message that is sent to the agent wrapper.

```
{
  "encrypt_iv": "abcdefgqaaq",
  "account_id": 13304,
  "encrypt_key": "xyzabcaaaaei",
  "binary_path": "acc-13304/app100040/5212a73a-4063-4ca6-965a-c7bdcab75486/binaries/",
  "encrypted_key": "aaaaaaa",
  "bucket_name": "sca-nonprod-binary-store-master",
  "scan_id": "ee001443-fe8e-4d59-87a1-632ef300cca1",
  "env": "test-env-1",
  "app_id": 100043,
  "app_ver_id": 200043,
  "scan_config_id": "5212a73a-4063-4ca6-965a-c7bdcab75486"
}
```

The `scan_config_id` field is probably a typo, hopefully that can be changed
soon. Also, I am hoping that we can fix some of these unnecessary wrapping of
JSON objects in strings too.

Because of the complexity of the message sent to the queue, I wrote a script to
convert plain ScanRequest json messages to the expected message that are sent
to the queue. The old Scan Request message representations are transformed by
the ruby script into the new message format, converted into strings, wrapped in
another JSON object, and base64 encoded, before being wrapped in a Agora
message object.
