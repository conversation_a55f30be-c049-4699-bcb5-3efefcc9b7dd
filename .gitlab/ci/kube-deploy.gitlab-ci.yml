.kube-deploy:
  variables:
    KUBECTL_VERSION: v1.14.0
    VERSION: $CI_COMMIT_SHA
    POD_IMAGE: ${DOCKER_PULL_HOST}/${CI_PROJECT_NAMESPACE}/${CI_PROJECT_NAME}:$CI_PIPELINE_ID
    REPLICAS: 1
  before_script:
  - apt-get update && apt-get -y install gettext-base
  - ls -la
  # https://kubernetes.io/docs/tasks/tools/install-kubectl/
  - &getkubectl |
    # $K8S_* variables are set in the .gitlab-ci.yml file
    if [[ -z $K8S_CLUSTER || -z $K8S_CLUSTER_SERVER || -z $K8S_USER || -z $K8S_USER_TOKEN ]]; then
        echo "Kubernetes variables are not defined"
        exit 1
    fi
    get_bin_kubectl() {
    cached_file=kubectl
    creates=/bin/kubectl
    if [[ -e $cached_file ]]; then
      if [[ ! -x   $cached_file ]]; then
        chmod 0755 $cached_file
      fi
      if [[ ! -e $creates ]]; then
        cp -v $cached_file $creates
      fi
    fi
    if [[ -x $creates ]]; then
      return
    elif [[ -e   $creates ]]; then
      chmod 0755 $creates
      return
    fi
    # just shorten the typing
    u=https://storage.googleapis.com/kubernetes-release/release
    wget -O "$creates" ${u}/${KUBECTL_VERSION}/bin/linux/amd64/kubectl
    find . -type f -exec ls -la '{}' ';'
    ls -la
    chmod 0755 $creates
    cp -v      $creates $cached_file
    ls -la     $creates $cached_file
    }
    get_bin_kubectl
  # this business is to keep it from using the ELB to leave the cluster and back in it
  - kubectl config set-cluster $K8S_CLUSTER --insecure-skip-tls-verify=true --server=$K8S_CLUSTER_SERVER
  - kubectl config set-credentials $K8S_USER --token=$K8S_USER_TOKEN
  - kubectl config set-context deploy --cluster=$K8S_CLUSTER --user=$K8S_USER
  - kubectl config set current-context deploy
  - envsubst < sca-dev-svc.yml | kubectl apply -n ${K8S_NAMESPACE} -f -
  - envsubst < sca-dev-secrets.yml | kubectl apply -n ${K8S_NAMESPACE} -f -
  - envsubst < sca-dev-scs-truststore-secrets.yml | kubectl apply -n ${K8S_NAMESPACE} -f -
  - envsubst < sca-dev-statefulset.yml | kubectl apply -n ${K8S_NAMESPACE} -f -
  only:
    - master