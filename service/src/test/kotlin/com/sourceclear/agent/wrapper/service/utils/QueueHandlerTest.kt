package com.sourceclear.agent.wrapper.service.utils

import com.sourceclear.agent.wrapper.service.queue.QueueHandler
import com.sourceclear.agent.wrapper.service.queue.unpackers.UNPACK_DIR_SUFFIX
import org.apache.commons.codec.binary.Hex
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.junit.jupiter.params.ParameterizedTest
import org.junit.jupiter.params.provider.Arguments
import org.junit.jupiter.params.provider.MethodSource
import org.testcontainers.shaded.org.apache.commons.io.FilenameUtils
import java.io.FileOutputStream
import java.nio.ByteBuffer
import java.nio.file.Files
import java.nio.file.NoSuchFileException
import java.nio.file.Path
import java.nio.file.Paths
import java.util.stream.Collectors
import kotlin.test.assertEquals
import kotlin.test.assertTrue
import java.util.UUID
import java.util.zip.ZipEntry
import java.util.zip.ZipOutputStream
import javax.crypto.CipherInputStream


class QueueHandlerTest {

  /**
   * If to debug unzipped resource in pod, add @Disabled to ignore this test
   * once you comment out TempResource's cleanup() method
   */
  @Test
  fun recursiveUnpackOnNestedZip() {
    val projectPaths = projectsAndEvidence()
        .map { it.get().component1() as String }
        .map { QueueHandlerTest::class.java.classLoader.getResource(it)!!.toURI() }
        .map { Paths.get(it) }

    val expectedEvidence = projectsAndEvidence().flatMap { it.get().component2() as Set<*> }.toSet()

    // the zipFile will be deleted by QueueHandler.unpackToDisk
    // because ZipUnpacker.unpack deletes archive files upon unpacking to save disk space
    assertThrows<NoSuchFileException> {
      CloseableTempIO.createTempFile("zip-of-zips", ".zip").use { zipFile ->
        ZipOutputStream(FileOutputStream(zipFile.toFile())).use { zipOutputStream ->
          // generate a repeating sequence of project paths so that we can test with a relatively big nested zip
          for (project in (0..5).flatMap { projectPaths }) {
            val zipEntry = ZipEntry(UUID.randomUUID().toString() + project.fileName.toString())
            zipOutputStream.putNextEntry(zipEntry)
            Files.copy(project, zipOutputStream)
            zipOutputStream.closeEntry()
          }
        }

        CloseableTempIO.createTempDirectory("test").use { tempDir ->
          val targetDir = QueueHandler.unpackToDisk(
              filename = "zip-of-zips.zip",
              archive = zipFile,
              into = tempDir,
              isArchivedFile = false
          )
          val evidence = QueueHandler.scan(targetDir).map { e -> Pair(e.coordinates.coordinate1, e.coordinates.version) }.toSet()
          assertTrue { evidence.containsAll(expectedEvidence) }
        }
      }
    }
  }

  @Test
  fun unpackOnFolderOfDeepZips() {
    val projectPaths = projectsAndEvidence()
        .map { it.get().component1() as String }
        .map { QueueHandlerTest::class.java.classLoader.getResource(it)!!.toURI() }
        .map { Paths.get(it) }

    val expectedEvidence = projectsAndEvidence().flatMap { it.get().component2() as Set<*> }.toSet()

    CloseableTempIO.createTempDirectory("folder-of-zips").use { dir ->
      val basePath = dir.resolve("binaries");
      val deepDirZip = Path.of("very-deep", "super-deep", "extremely-deep").toString()
      val deepDir = Path.of(basePath.toString(), deepDirZip)
      Files.createDirectories(deepDir)
      val files = projectPaths.map {
        Files.copy(it, Path.of(deepDir.toString(), it.fileName.toString()))
      }

      CloseableTempIO.createTempDirectory("test").use { tempDir ->
        files.forEach {
          QueueHandler.unpackToDisk(
              filename = it.toString(),
              archive = it,
              into = tempDir,
              basePath = basePath.toString(),
              isArchivedFile = false)
        }
        val evidence = QueueHandler.scan(tempDir)
        val coordAndVersionEvidence = evidence.map { e -> Pair(e.coordinates.coordinate1, e.coordinates.version) }.toSet()
        assertTrue { coordAndVersionEvidence.containsAll(expectedEvidence) }
        val filepathEvidence = evidence
            .flatMap { it.evidencePaths }
            .filterNotNull()
            .map { it.filePath }
        filepathEvidence.forEach { assertTrue(it.startsWith(deepDirZip)) }
      }
    }
  }

  @Test
  fun testUnpackJarAes() {
    val hex = Hex()
    val key = hex.decode(ByteBuffer.wrap("27e08ca1edec290fccd8e69b29b7b5784c9de82924dfac2bb3bbfdcddb5e0843".toByteArray()))
    val iv = hex.decode(ByteBuffer.wrap("f7d7a723a479d0ae32454eff472a0282".toByteArray()))
    val cipher = aesCipherWith(key, iv)
    val stream = CipherTest::class.java.classLoader.getResourceAsStream("commons-fileupload-1.2.jar.aes")
    val filename = "17/acct12417/app13738/ver14428/binaries/commons-fileupload-1.2.jar.aes"

    CloseableTempIO.createTempDirectory("uuid").use { dir ->
      val archive = CipherInputStream(stream, cipher).use { inputStream ->
        QueueHandler.writeToDisk(inputStream, dir, FilenameUtils.getName(filename))
      }
      val recursiveUnpack = System.getenv("UNPACK_RECURSIVELY") == "true"
      val targetDir = QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = dir,
          basePath = "17/acct12417/app13738/ver14428",
          isArchivedFile = false,
          recursive = recursiveUnpack)

      assertTrue(Files.exists(targetDir))
      assertEquals("binaries", targetDir.fileName.toString())
      assertEquals(1, targetDir.toFile().listFiles()!!.filter { it.extension == "jar" }.size)
    }
  }

  @Test
  fun testUnpackZipAsArchivedFile() {
    val filename = "foo.zip"
    val testArchive = Paths.get(QueueHandler::class.java.classLoader.getResource(filename)!!.toURI())

    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      val targetDir = QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = true
      )
      val files = Files.walk(targetDir).map { tempDir.relativize(it) }.filter { it != Paths.get("") }.collect(Collectors.toSet()).toSet()
      val expectedFiles = setOf("foo", "foo/some-file", "foo/bar", "foo/bar/some-file").map { Paths.get(it) }.toSet()
      assertEquals(tempDir, targetDir)
      assertEquals(expectedFiles, files)
    }
  }

  @Test
  fun testUnpackZipAsNonArchivedFile() {
    val filename = "foo.zip"
    val testArchive = Paths.get(QueueHandler::class.java.classLoader.getResource(filename)!!.toURI())

    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      val targetDir = QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = false
      )
      val files = Files.walk(targetDir).map { tempDir.relativize(it) }.filter { it != Paths.get("") }.collect(Collectors.toSet()).toSet()
      val expectedFiles = setOf("foo.zip$UNPACK_DIR_SUFFIX", "foo.zip$UNPACK_DIR_SUFFIX/foo", "foo.zip$UNPACK_DIR_SUFFIX/foo/bar", "foo.zip$UNPACK_DIR_SUFFIX/foo/bar/some-file", "foo.zip$UNPACK_DIR_SUFFIX/foo/some-file").map { Paths.get(it) }.toSet()
      assertEquals(tempDir, targetDir)
      assertEquals(expectedFiles, files)
    }
  }

  @ParameterizedTest
  @MethodSource("projectsAndEvidence")
  fun testScanExamplePythonPipenv(filename: String, expectedEvidence: Set<Pair<String, String>>) {
    val testArchive = Paths.get(QueueHandler::class.java.classLoader.getResource(filename)!!.toURI())

    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = false
      ).let {
        val evidence = QueueHandler.scan(it).map { e -> Pair(e.coordinates.coordinate1, e.coordinates.version) }.toSet()
        assertTrue { evidence.containsAll(expectedEvidence) }
      }
    }

  }

  @Test
  fun testUnpackRbv() {
    val filename = "rbv-sample.zip"
    val testArchive = Paths.get(QueueHandler::class.java.classLoader.getResource(filename)!!.toURI())

    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      val targetDir = QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = true
      )
      val files = Files.walk(targetDir).map { tempDir.relativize(it) }.filter { it != Paths.get("") }.collect(Collectors.toSet()).toSet()
      val expectedFiles = setOf("rbv-sample", "rbv-sample/rbv.rbv$UNPACK_DIR_SUFFIX/project/Gemfile.lock", "rbv-sample/rbv.rbv$UNPACK_DIR_SUFFIX/veracode-project-20191121101936",
          "rbv-sample/rbv.rbv$UNPACK_DIR_SUFFIX/project", "rbv-sample/rbv.rbv$UNPACK_DIR_SUFFIX/veracode-project-20191121101936/project/Gemfile.lock", "rbv-sample/rbv.rbv$UNPACK_DIR_SUFFIX",
          "rbv-sample/rbv.rbv$UNPACK_DIR_SUFFIX/veracode-project-20191121101936/project").map { Paths.get(it) }.toSet()

      assertEquals(expectedFiles, files)
    }

  }

  @Test
  fun testUnpackWar() {
    val filename = "zip-war.zip"
    val testArchive = Paths.get(QueueHandler::class.java.classLoader.getResource(filename)!!.toURI())

    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      val targetDir = QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = true
      )
      val files = Files.walk(targetDir)
          .map { tempDir.relativize(it) }
          .filter { it != Paths.get("") }
          .collect(Collectors.toSet()).toSet()
      val expectedFiles = setOf("commons-inside.war$UNPACK_DIR_SUFFIX/commons-io-2.6.jar", "commons-inside.war",
          "commons-inside.war$UNPACK_DIR_SUFFIX", "commons-inside.war$UNPACK_DIR_SUFFIX/commons-io-2.6.jar$UNPACK_DIR_SUFFIX")
          .map { Paths.get(it) }.toSet()

      assertEquals(expectedFiles, files)
    }

  }

  @Test
  fun testUnpackPhpFile() {
    val filename = "PHP.php.aes"
    val testArchive = Paths.get(QueueHandler::class.java.classLoader.getResource(filename)!!.toURI())

    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      val targetDir = QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = false
      )
      assertTrue(targetDir.toFile().exists())
      assertEquals(tempDir, targetDir)
      assertEquals(1, targetDir.toFile().listFiles()!!.filter { it.extension == "php" }.size)
    }

  }

  @Test
  fun testUnpackRawPhpFile() {
    val filename = "PHP.php"
    val testArchive = Paths.get(QueueHandler::class.java.classLoader.getResource(filename)!!.toURI())

    CloseableTempIO.createTempDirectory("test").use { tempDir ->
      val archive = Files.copy(testArchive, tempDir.resolve(filename))
      val targetDir = QueueHandler.unpackToDisk(
          filename = filename,
          archive = archive,
          into = tempDir,
          isArchivedFile = false
      )
      assertTrue(targetDir.toFile().exists())
      assertEquals(tempDir, targetDir)
      assertEquals(1, targetDir.toFile().listFiles()!!.filter { it.extension == "php" }.size)
    }
  }

  companion object {
    @JvmStatic
    fun projectsAndEvidence() = listOf(
        Arguments.of("example-python-pipenv.zip", setOf(
            Pair("rsa", "3.4"),
            Pair("feedparser", "5.1.1"),
            Pair("pyasn1", "0.4.3"),
            Pair("pycrypto", "2.4"),
            Pair("pyjwt", "0.4.2"),
            Pair("django", "1.7.1"),
            Pair("requests", "2.2.1"))),
        Arguments.of("example-ruby.zip", setOf(
            Pair("warden", "1.2.6"),
            Pair("spring", "1.7.1"),
            Pair("sass", "3.4.22"))),
        Arguments.of("example-javascript-yarn.zip", setOf(
            Pair("concat-map", "0.0.1"),
            Pair("cookie-signature", "1.0.2"),
            Pair("cryptiles", "2.0.5"))),
        Arguments.of("empty.zip", setOf<Pair<String, String>>()))
  }
}