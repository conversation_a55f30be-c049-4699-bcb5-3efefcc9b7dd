FROM --platform=amd64 docker-ro.laputa.veracode.io/sca/base-images/base-java-17:17.0.12-17.52-jre
#
# NOTE: alpine containers by default has ulimit core file size of 0, thereby will not generate core dump files (try running "ulimit -c" to confirm).
#

RUN apk update && \
    apk upgrade
RUN apk add icu-libs gcompat

COPY service/target/service-*[^sources].jar /agent-wrapper.jar
COPY bundletool-all/bundletool-all-1.13.1.jar /bundletool-all-1.13.1.jar

COPY dockerfile-entrypoint.sh /

# Set some defaults but mostly overridden at run
ENV JAVA_OPTS="-Xms2500M -Xmx2500M \
    -Dcom.sun.management.jmxremote \
    -Dcom.sun.management.jmxremote.authenticate=false \
    -Dcom.sun.management.jmxremote.port=8888 \
    -Dcom.sun.management.jmxremote.ssl=false \
    -Dcom.sun.management.jmxremote.local.only=false \
    -Djava.rmi.server.hostname=localhost" \
 MANAGEMENT_SERVER_PORT=8080 \
 LANG=C.UTF-8 \
 LANGUAGE=C.UTF-8 \
 LC_ALL=C.UTF-8

EXPOSE 8080
ENTRYPOINT ["./dockerfile-entrypoint.sh"]
