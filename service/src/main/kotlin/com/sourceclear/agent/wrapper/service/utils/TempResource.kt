package com.sourceclear.agent.wrapper.service.utils

import java.nio.file.Files
import java.nio.file.Path

/**
 * A data class that wraps a Path object so that we can define `use` on them.
 * I can extend Path with the use function but that wouldn't help us to differentiate
 * between a file and a directory. The temp folder will be deleted when done scanning,
 * if to debug the unarchive, please comment out cleanup() method on stage environment only,
 * for production environment, cleanup() should be enabled all the time!
 */
sealed class TempResource(open val path: Path) {
  fun <T> use(function: (Path) -> T): T {
    try {
      return function(path)
    } finally {
      //To debug the unzipped scan packages,
      //you may uncomment the following cleanup() method,
      //once the scan is done, the temporary folder will NOT be deleted
      //Note: !!!debug only in STAGE environment!!!
      cleanup()
    }
  }

  abstract fun cleanup()
}

data class TempFile(override val path: Path) : TempResource(path) {
  override fun cleanup() {
    Files.delete(path)
  }
}

data class TempDirectory(override val path: Path) : TempResource(path) {
  override fun cleanup() {
    path.toFile().deleteRecursively()
  }
}
