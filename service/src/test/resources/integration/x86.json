[{"evidenceId": "0b952674-b299-4ac2-9ad3-170716e6b6cd", "evidencePaths": [{"filePath": "libOpenSLES.so", "lineNumber": null, "dependencyPath": [{"@id": 1, "coordinate1": "libfilterengine.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}]}], "coordinates": {"@id": 2, "coordinate1": "libOpenSLES.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "9cdaa37b-72f0-42eb-9700-7dc1e632a2b2", "evidencePaths": [{"filePath": "x86.zip#zip:x86/libfilterengine.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 3, "coordinate1": "libfilterengine.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "553038d915e948328d51fe5a241865018a6e8dd6abc55de4be110f3c61d9807b", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "26427c3d-6076-48f8-b405-6461080681aa", "evidencePaths": [{"filePath": "x86.zip#zip:x86/libsqlcipher.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 4, "coordinate1": "libsqlcipher.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "6819096b8f3ddd565d930cf981d21ba601e760651f2ef366318d2c1e01cf4a64", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "0dc4c4d7-6004-4dec-b9de-3e75eee60bbd", "evidencePaths": [{"filePath": "libc.so", "lineNumber": null, "dependencyPath": [{"@id": 5, "coordinate1": "libucs-credential.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}]}], "coordinates": {"@id": 6, "coordinate1": "libc.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "01584b01-ec37-41c1-8f1f-9abd45f9ac6b", "evidencePaths": [{"filePath": "libandroid.so", "lineNumber": null, "dependencyPath": [1]}], "coordinates": {"@id": 7, "coordinate1": "libandroid.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "17971b73-05ce-4a22-9690-c139de3afd59", "evidencePaths": [{"filePath": "liblog.so", "lineNumber": null, "dependencyPath": [5]}], "coordinates": {"@id": 8, "coordinate1": "liblog.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "6f750166-02d8-45b7-b845-d755c20bf360", "evidencePaths": [{"filePath": "libm.so", "lineNumber": null, "dependencyPath": [5]}], "coordinates": {"@id": 9, "coordinate1": "libm.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "fd529c64-f7c1-4ec2-905e-b6f60f1e9098", "evidencePaths": [{"filePath": "x86.zip#zip:x86/libucs-credential.so", "lineNumber": null, "dependencyPath": []}], "coordinates": {"@id": 10, "coordinate1": "libucs-credential.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "28f3304735b75a8b52b5599888f65df540a63d0fe5f2329f1a238fba3fddce54", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}, {"evidenceId": "561f8438-9b55-41cd-be6f-fbc9295f04b2", "evidencePaths": [{"filePath": "libdl.so", "lineNumber": null, "dependencyPath": [5]}], "coordinates": {"@id": 11, "coordinate1": "libdl.so", "coordinate2": null, "version": "<undefined>", "platform": null, "scope": null, "coordinateType": "SO", "buildType": "SO"}, "sha1": null, "sha2": "", "bytecodeHash": null, "commitHash": null, "evidenceType": "SO"}]