[{"evidenceId": "d590d200-f337-40a7-b84c-5cf0cd7e7c57", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 29, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L29"}], "coordinates": {"coordinate1": "github.com/mattn/go-colorable", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "d228849504861217f796da67fae4f6e347643f15", "evidenceType": "COMMIT"}, {"evidenceId": "e89ae10f-9e08-46d2-a967-d97ee727e816", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 5, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L5"}], "coordinates": {"coordinate1": "github.com/bitly/go-simplejson", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "da1a8928f709389522c8023062a3739f3b4af419", "evidenceType": "COMMIT"}, {"evidenceId": "4f6c34d6-4a7c-4f58-ad1b-1b1604ba2e77", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 83, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L83"}], "coordinates": {"coordinate1": "github.com/square/go-jose", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "7f0dd807d3f3d73bb536898cb7980ddf638ce69a", "evidenceType": "COMMIT"}, {"evidenceId": "e768c1ff-fd1b-4cc9-b823-67ff73c9b817", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 53, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L53"}], "coordinates": {"coordinate1": "github.com/nwidger/jsoncolor", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "0192e84d44af834c3a90c8a17bf670483b91ad5a", "evidenceType": "COMMIT"}, {"evidenceId": "6775e455-7541-4d26-8de0-5d11ea4e4802", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 41, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L41"}], "coordinates": {"coordinate1": "github.com/mattn/go-runewidth", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "14207d285c6c197daabb5c9793d63e7af9ab2d50", "evidenceType": "COMMIT"}, {"evidenceId": "23dd4249-2f56-43fc-8ee4-3a6e87affa5b", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 35, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L35"}], "coordinates": {"coordinate1": "github.com/mattn/go-isatty", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "30a891c33c7cde7b02a981314b4228ec99380cca", "evidenceType": "COMMIT"}, {"evidenceId": "f86cfbe9-6927-4b8e-bd5c-d18f0b324f1b", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 17, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L17"}], "coordinates": {"coordinate1": "github.com/google/go-github", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "27c7c32b6d369610435bd2ad7b4d8554f235eb01", "evidenceType": "COMMIT"}, {"evidenceId": "3cbf264a-0c23-490f-a929-58fed3f91581", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 47, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L47"}], "coordinates": {"coordinate1": "github.com/nsf/termbox-go", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "abe82ce5fb7a42fbd6784a5ceb71aff977e09ed8", "evidenceType": "COMMIT"}, {"evidenceId": "21f14f13-39bf-4ec1-8288-0eddeb65db49", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 89, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L89"}], "coordinates": {"coordinate1": "golang.org/x/sys", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "7a6e5648d140666db5d920909e082ca00a87ba2c", "evidenceType": "COMMIT"}, {"evidenceId": "854a3cdf-51f1-442f-ad0e-57ea6bb105fc", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 23, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L23"}], "coordinates": {"coordinate1": "github.com/google/go-querystring", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "53e6ce116135b80d037921a7fdd5138cf32d7a8a", "evidenceType": "COMMIT"}, {"evidenceId": "25a9c2b7-6750-4b56-9182-95a30f851e0f", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 11, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L11"}], "coordinates": {"coordinate1": "github.com/fatih/color", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "42c364ba490082e4815b5222728711b3440603eb", "evidenceType": "COMMIT"}, {"evidenceId": "4d19fd20-0a7a-40b4-8882-8f89126e687d", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 59, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L59"}], "coordinates": {"coordinate1": "github.com/pkg/errors", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "248dadf4e9068a0b3e79f02ed0a610d935de5302", "evidenceType": "COMMIT"}, {"evidenceId": "0b1715a3-cf72-43fb-9395-378e5300cda6", "evidencePaths": [{"filePath": "govendor.zip#zip:vendor/vendor.json", "lineNumber": 65, "dependencyPath": [], "filePathWithLineNumber": "govendor.zip#zip:vendor/vendor.json#L65"}], "coordinates": {"coordinate1": "github.com/simeji/jid", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "9090f06e9643aa1a4e95904a4ebbd6d186b3a20f", "evidenceType": "COMMIT"}]