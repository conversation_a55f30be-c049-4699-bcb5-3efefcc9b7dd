package com.sourceclear.agent.wrapper.service.utils

import com.sourceclear.agent.wrapper.service.queue.QueueHandler
import com.sourceclear.agent.wrapper.service.queue.unpackers.GZipUnpacker
import com.sourceclear.agent.wrapper.service.queue.unpackers.JsInJarUnpacker
import com.sourceclear.agent.wrapper.service.queue.unpackers.Unpacker
import com.sourceclear.agent.wrapper.service.queue.unpackers.ZipUnpacker
import com.sourceclear.agent.wrapper.service.queue.unpackers.ApkArchiveUnpacker
import com.sourceclear.agent.wrapper.service.queue.unpackers.TarUnpacker
import org.junit.Test
import org.junit.runner.RunWith
import org.junit.runners.Parameterized
import java.nio.file.Path
import java.nio.file.Paths
import kotlin.test.assertEquals

@RunWith(Parameterized::class)
class DetermineUnpackerTest(private val file: Path,
                            private val expectedUnpacker: Unpacker?,
                            private val unpackers: List<Unpacker>) {

  companion object {
    @JvmStatic
    @Parameterized.Parameters
    fun data(): List<Array<out Any?>> {
      val getResource = { resource: String -> Paths.get(DetermineUnpackerTest::class.java.classLoader.getResource(resource).toURI()) }
      return listOf(
        arrayOf(getResource("foo.tar.gz"), GZipUnpacker.instance, Unpacker.unpackers),
        arrayOf(getResource("foo.zip"), ZipUnpacker.instance, Unpacker.unpackers),
        arrayOf(getResource("fooUpperCase.ZIP"), ZipUnpacker.instance, Unpacker.unpackers),
        arrayOf(getResource("fooUpperCase.ZIP.aes"), ZipUnpacker.instance, Unpacker.unpackers),
        arrayOf(getResource("fooUpperCaseAll.ZIP.AES"), ZipUnpacker.instance, Unpacker.unpackers),
        arrayOf(getResource("foo.zip"), ZipUnpacker.instance, Unpacker.recursiveUnpackers),
        arrayOf(getResource("foo.jar"), null, Unpacker.unpackers),
        arrayOf(getResource("foo.jar"), JsInJarUnpacker.instance, Unpacker.recursiveUnpackers),
        arrayOf(getResource("commons-io-2.6.jar"), null, Unpacker.unpackers),
        arrayOf(getResource("foo.txt"), null, Unpacker.recursiveUnpackers),
        arrayOf(getResource("foo.apk"), ApkArchiveUnpacker.instance, Unpacker.unpackers),
        arrayOf(getResource("foo.apk"), ApkArchiveUnpacker.instance, Unpacker.recursiveUnpackers),
        arrayOf(getResource("foo.aar"), null, Unpacker.unpackers),
        arrayOf(getResource("foo.aar"), null, Unpacker.recursiveUnpackers),
        arrayOf(getResource("foo.tar"), TarUnpacker.instance, Unpacker.recursiveUnpackers),
      )
    }
  }

  @Test
  internal fun testDetermineUnpacker() {
    assertEquals(expectedUnpacker, QueueHandler.determineUnpacker(file, unpackers))
  }
}
