To capture evidences for any package, such as, .jar, .zip, .apk etc, copy it into `integration-candidate` folder.
Run `QueueHandlerIntegrationTest.scan capture()`, it will generate corresponding .json evidences. Once we think
they can graduate as test cases, the package and the captured JSON should be promoted to `integration` folder
as final tests.

Regarding the following test pairs, see ticket SCA-11853 about the `SO` collector bug.
* (android-sample-n-arch.apk, android-sample-n-arch.json)
* (multi-arch.zip, multi-arch.json)
* aa.zip, bb.zip, cc.zip - AABs that also have `SO` issues