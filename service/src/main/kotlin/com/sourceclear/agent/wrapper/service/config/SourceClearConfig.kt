package com.sourceclear.agent.wrapper.service.config

import com.amazon.sqs.javamessaging.AmazonSQSExtendedClient
import com.amazon.sqs.javamessaging.ExtendedClientConfiguration
import com.fasterxml.jackson.databind.ObjectMapper
import com.github.kittinunf.result.Result
import com.sourceclear.agent.wrapper.http.HttpError
import com.sourceclear.agent.wrapper.sourceclear.client.SourceClearClient
import com.sourceclear.api.data.match.VeracodeScan
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.stereotype.Component
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.sqs.SqsClient
import software.amazon.awssdk.services.sqs.model.GetQueueUrlRequest
import software.amazon.awssdk.services.sqs.model.SendMessageRequest
import java.net.URI

@Component
@ConfigurationProperties(prefix = "srcclr")
class SourceClearConfig {

  var ishttp: Boolean = false

  /**
   * The URL of the sourceclear platform API
   */
  lateinit var url: String

  /**
   * The signing key to be used to sign the jwt tokens
   */
  lateinit var key: String

  @Deprecated("This is deprecated in favour of the SQS communication")
  @Bean
  fun sourceClearClient(): SourceClearClient {
    return SourceClearClient(URI.create(url), key.trim('\n').trim())
  }

  /**
   * The queue used to communicate with the SourceClear platform-backend
   */
  lateinit var queue: String

  /**
   * The bucket to be used for the large payload between agent-wrapper and SourceClear platform-backend
   */
  lateinit var bucket: String

  @Bean
  fun amazonSQSExtendedClient(@Qualifier("sqsClient") sqsClient: SqsClient,
                              @Qualifier("queueMessage") amazonS3Client: S3Client): AmazonSQSExtendedClient {
    return AmazonSQSExtendedClient(sqsClient, ExtendedClientConfiguration().withPayloadSupportEnabled(amazonS3Client, bucket))
  }

  @Bean
  fun sourceClearQueue(amazonSQSExtendedClient: AmazonSQSExtendedClient,
                       sourceClearClient: SourceClearClient): SourceClearCommunication {
    return SourceClearCommunication(amazonSQSExtendedClient, queue.trim('\n').trim(), sourceClearClient, ishttp)
  }
}

class SourceClearCommunication(private val amazonSqs: AmazonSQSExtendedClient,
                               private val queueName: String,
                               private val sourceClearClient: SourceClearClient,
                               private val isHttp: Boolean) {

  private val queueUrl: String = amazonSqs.getQueueUrl(GetQueueUrlRequest.builder().queueName(queueName).build()).queueUrl()

  private val objectMapper = ObjectMapper()

  fun veracodeScan(veracodeScanPayload: VeracodeScan): Result<String, HttpError> {
    return if (isHttp) {
      sourceClearClient.veracodeScan(veracodeScanPayload, 3)
    } else {
      val payload = objectMapper.writeValueAsString(veracodeScanPayload)
      // relying on aws sdk to throw exception to signal communication failure
      amazonSqs.sendMessage(SendMessageRequest.builder().queueUrl(queueUrl).messageBody(payload).build())
      Result.success("Message put")
    }
  }
}