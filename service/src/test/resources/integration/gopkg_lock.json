[{"evidenceId": "afc984e1-ca23-44b2-ba8f-e088f6d17063", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/google/go-querystring", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "53e6ce116135b80d037921a7fdd5138cf32d7a8a", "evidenceType": "COMMIT"}, {"evidenceId": "9a9de870-bfb6-4ae1-b1de-7a9aec33ef1e", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/google/go-github", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "173908142398084e14d7f14c39924f0004d43679", "evidenceType": "COMMIT"}, {"evidenceId": "79ddafe0-b9dd-47e7-b918-66fa00db2318", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/mattn/go-runewidth", "coordinate2": null, "version": "v0.0.2", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "e1bac03d-c8a5-457e-a5a2-b8d984d2be0f", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/mattn/go-isatty", "coordinate2": null, "version": "v0.0.3", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "e85eb309-ed22-459f-b6a9-6a341a0a6da8", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/nsf/termbox-go", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "aa4a75b1c20a2b03751b1a9f7e41d58bd6f71c43", "evidenceType": "COMMIT"}, {"evidenceId": "dfdc4c85-d920-43de-af3a-78e1b5c99f05", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/square/go-jose", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "7f0dd807d3f3d73bb536898cb7980ddf638ce69a", "evidenceType": "COMMIT"}, {"evidenceId": "1f007ba6-bd99-4599-a304-048b21b44b2e", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/simeji/jid", "coordinate2": null, "version": "0.7.2", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "88386614-42ff-4d9a-9242-e32a1ab9e37f", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/fatih/color", "coordinate2": null, "version": "v1.5.0", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "19ffd76c-2e27-4355-a7b4-f050b81bae5b", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/srcclr/example-go-golangdep", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "e869bf347e2196a2d0512920c88db027d4d98ad9", "evidenceType": "COMMIT"}, {"evidenceId": "217a537b-508e-46b3-b5c0-5e8fffb17ea1", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/nwidger/jsoncolor", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "75a6de4340e59be95f0884b9cebdda246e0fdf40", "evidenceType": "COMMIT"}, {"evidenceId": "5e1c5dfa-9fba-4717-ae8f-f26b33ea1da3", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/pkg/errors", "coordinate2": null, "version": "v0.8.0", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "ef3aca36-cbdc-489f-90f6-85ee32a06e0d", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/mattn/go-colorable", "coordinate2": null, "version": "v0.0.9", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "caa395a6-a7b6-4937-bb10-da980c00e627", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "github.com/bitly/go-simplejson", "coordinate2": null, "version": "v0.5.0", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "c8944418-dac3-4296-b542-f953fb8c35ce", "evidencePaths": [{"filePath": "gopkg_lock.zip#zip:Gopkg.lock", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "gopkg_lock.zip#zip:Gopkg.lock"}], "coordinates": {"coordinate1": "golang.org/x/sys", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "054c452bb702e465e95ce8e7a3d9a6cf0cd1188d", "evidenceType": "COMMIT"}]