package com.sourceclear.agent.wrapper.service.scan

import com.sourceclear.engine.component.ComponentEngineBuilder
import org.junit.jupiter.api.Test
import kotlin.test.assertEquals

class UploadScanAttributesTest {

  @Test
  fun testDynamicAttributes() {
     val actualAttributes = UploadScanAttributes.dynamicAttributes(2)
    assertEquals(actualAttributes.get(ComponentEngineBuilder.FAT_JAR_MAX_RECURSION_DEPTH), 2)
    assertEquals(actualAttributes.get(ComponentEngineBuilder.FAT_AAR_MAX_RECURSION_DEPTH), 2)
  }
}