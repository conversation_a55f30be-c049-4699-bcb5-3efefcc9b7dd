spring:
  main:
    banner-mode: "off"
  autoconfigure:
    exclude:
      - org.springframework.cloud.aws.autoconfigure.context.ContextStackAutoConfiguration
      - org.springframework.cloud.aws.autoconfigure.context.ContextRegionProviderAutoConfiguration

# If testing locally, remember to set your aws credentials in ~/.aws/credentials or you may set these environment variables:
# - AWS_ACCESS_KEY_ID
# - AWS_SECRET_ACCESS_KEY
# - AWS_REGION

sqs.queue: jsyeo-scan-request

# 3 hour timeout
# this value is transformed to seconds so it can't be less than 1s
scan.timeout: 3h

scanconfig:
  url: https://sca-scanconfig-service
  port: ${SCA_SCANCONFIG_SERVICE_SERVICE_PORT:443}
  # No problem committing this here. This only contains public certs.
  truststorePath: scs.jks
  truststorePassword: the-qa-password
  # Use a dummy keystore instead of committing our private keys. YIKES!
  # This is so that spring is happy and it starts up fine.
  keystorePath: dummy-keystore.jks
  keystorePassword: changeit

srcclr:
  url: https://api.ops2.srcclr.io
  # The signing key in PKCS#8 format
  # use `openssl pkcs8 -topk8 -in signing_key.pem  -inform pem -out signing_key_pkcs8.pem -outform pem -nocrypt`
  # to convert PKCS#1 private keys to the PKCS#8 format
  key: |
    -----BEGIN PRIVATE KEY-----
    FOOBARFOOBARFOOBAR
    FOOBARFOOBARFOOBAR
    FOOBARFOOBARFOOBAR
    -----END PRIVATE KEY-----
  queue: jsyeo-srcclr-evidence
  bucket: jyeo-test

# Bundletool path in pod
bundletool:
  path: "/bundletool-all-1.13.1.jar"

logging:
  group:
    burrito: com.sourceclear.agent.wrapper
    engine: com.sourceclear.engine
#  level:
#    burrito: debug
#    engine: debug
# enable debug to display evidences collected

management:
  server:
    ssl:
      enabled: false
  endpoints:
    web:
      exposure:
        include: "*"
  endpoint:
    health:
      show-details: always
