[{"evidenceId": "b1c3fef7-2cf9-4985-b7fb-269c5d052f5e", "evidencePaths": [{"filePath": "bower.zip#zip:bower_components/mapbox.js/bower.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "bower.zip#zip:bower_components/mapbox.js/bower.json"}], "coordinates": {"coordinate1": "mapbox.js", "coordinate2": null, "version": "1.6.4", "platform": null, "scope": null, "coordinateType": "BOWER"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "c2cef58a-97b5-437a-977b-dc48617bae95", "evidencePaths": [{"filePath": "bower.zip#zip:bower_components/jquery/bower.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "bower.zip#zip:bower_components/jquery/bower.json"}], "coordinates": {"coordinate1": "j<PERSON>y", "coordinate2": null, "version": "1.7.1", "platform": null, "scope": null, "coordinateType": "BOWER"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "193a0763-423b-4e69-8208-3ea130468a58", "evidencePaths": [{"filePath": "bower.zip#zip:bower_components/dojo/bower.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "bower.zip#zip:bower_components/dojo/bower.json"}], "coordinates": {"coordinate1": "dojo", "coordinate2": null, "version": "1.3.0", "platform": null, "scope": null, "coordinateType": "BOWER"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "87546bff-afea-4607-9ebb-6cd360f27efc", "evidencePaths": [{"filePath": "bower.zip#zip:bower_components/angular/bower.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "bower.zip#zip:bower_components/angular/bower.json"}], "coordinates": {"coordinate1": "angular", "coordinate2": null, "version": "1.0.8", "platform": null, "scope": null, "coordinateType": "BOWER"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "e8738c8c-3238-4694-930e-9853cc79c94c", "evidencePaths": [{"filePath": "bower.zip#zip:bower_components/validator-js/bower.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "bower.zip#zip:bower_components/validator-js/bower.json"}], "coordinates": {"coordinate1": "validator-js", "coordinate2": null, "version": "1.0.0", "platform": null, "scope": null, "coordinateType": "BOWER"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "837e8eb1-4c84-4684-8de6-1f139300381b", "evidencePaths": [{"filePath": "bower.zip#zip:bower_components/handlebars/bower.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "bower.zip#zip:bower_components/handlebars/bower.json"}], "coordinates": {"coordinate1": "handlebars", "coordinate2": null, "version": "4.7.7", "platform": null, "scope": null, "coordinateType": "BOWER"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "2e0a45df-edfb-4654-a2a4-ceda4d62c358", "evidencePaths": [{"filePath": "bower.zip#zip:bower_components/ember/bower.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "bower.zip#zip:bower_components/ember/bower.json"}], "coordinates": {"coordinate1": "ember", "coordinate2": null, "version": "1.2.0", "platform": null, "scope": null, "coordinateType": "BOWER"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}]