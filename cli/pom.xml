<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>com.sourceclear.agent.wrapper</groupId>
  <artifactId>cli</artifactId>
  <version>1.0.125-SNAPSHOT</version>

  <packaging>jar</packaging>

  <name>CLI</name>
  <description>CLI frontend to test scans</description>

  <parent>
    <groupId>com.sourceclear.agent.wrapper</groupId>
    <artifactId>wrapper-parent</artifactId>
    <version>1.0.125-SNAPSHOT</version>
  </parent>

  <dependencies>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib-jdk8</artifactId>
    </dependency>

    <dependency>
      <groupId>com.sourceclear.agent.wrapper</groupId>
      <artifactId>clients</artifactId>
      <version>1.0.125-SNAPSHOT</version>
    </dependency>

    <dependency>
      <groupId>com.sourceclear.engine</groupId>
      <artifactId>component</artifactId>
      <version>${engines.version}</version>
    </dependency>

    <!-- override vulnerable guava from component -->
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
    </dependency>
  </dependencies>

  <build>
    <sourceDirectory>${project.basedir}/src/main/kotlin</sourceDirectory>
    <testSourceDirectory>${project.basedir}/src/test/kotlin</testSourceDirectory>
    <plugins>
      <plugin>
        <artifactId>kotlin-maven-plugin</artifactId>
        <groupId>org.jetbrains.kotlin</groupId>
        <version>${kotlin.version}</version>

        <executions>
          <execution>
            <id>compile</id>
            <goals>
              <goal>compile</goal>
            </goals>
          </execution>

          <execution>
            <id>test-compile</id>
            <goals>
              <goal>test-compile</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
