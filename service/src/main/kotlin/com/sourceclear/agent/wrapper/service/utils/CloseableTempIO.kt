package com.sourceclear.agent.wrapper.service.utils

import java.nio.file.Files
import java.nio.file.Path

object CloseableTempIO {
  fun createTempDirectory(dir: Path, prefix: String): TempDirectory = TempDirectory(Files.createTempDirectory(dir, prefix))

  fun createTempDirectory(prefix: String): TempDirectory = TempDirectory(Files.createTempDirectory(prefix))

  fun createTempFile(prefix: String, suffix: String): TempFile = TempFile(Files.createTempFile(prefix, suffix))
}
