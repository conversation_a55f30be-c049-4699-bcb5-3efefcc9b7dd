package com.sourceclear.agent.wrapper.service.objects

import com.fasterxml.jackson.annotation.JsonAutoDetect
import com.fasterxml.jackson.annotation.JsonProperty
import com.fasterxml.jackson.databind.annotation.JsonNaming
import com.fasterxml.jackson.databind.PropertyNamingStrategy


@JsonNaming(PropertyNamingStrategy.SnakeCaseStrategy::class)
/**
 * A message sent from the SQS producer to trigger a scan in the agent wrapper
 *
 * @property encryptIv The hex encoded encryption iv that was used to encrypt the binary.
 * @property encryptKey The hex encoded encryption key that was used to encrypt the binary.
 * @property binaryPath The S3 path where the binaries are located
 * @property encryptedKey The encrypted key is a hex encoded and KMS encrypted string that consists
of the binary form of the encryption key and IV concatenated into a single
byte array. It is created this way in pseudo code:

encryptedKey = hexEncode(kmsEncrypt(binKey + binIv))
 * @property bucketName The S3 bucket where the binaries are located
 *
 * All the other properties doesn't really matter in the context of the agent wrapper.
 */
@JsonAutoDetect(fieldVisibility = JsonAutoDetect.Visibility.ANY)
data class ScanRequestMessage(@JsonProperty("encrypt_iv") val encryptIv: String?,
                              @JsonProperty("encrypt_key") val encryptKey: String?,
                              @JsonProperty("account_id") val accountId: Long,
                              @JsonProperty("binary_path") val binaryPath: String,
                              @JsonProperty("encrypted_key") val encryptedKey: String?,
                              @JsonProperty("bucket_name") val bucketName: String,
                              @JsonProperty("scan_id") val scanId: String,
                              @JsonProperty("job_id") val jobId: Long,
                              @JsonProperty("env") val env: String?,
                              @JsonProperty("app_id") val appId: Long,
                              @JsonProperty("app_name") val appName: String?,
                              @JsonProperty("sandbox_id") val sandboxId: Long,
                              @JsonProperty("sandbox") val sandbox: Boolean,
                              @JsonProperty("app_ver_id") val appVerId: Long,
                              @JsonProperty("analysis_id") val analysisId: Long?,
                              @JsonProperty("analysis_unit_id") val analysisUnitId: Long,
                              @JsonProperty("scan_config_id") val scanConfigId: String,
                              @JsonProperty("sca_enabled") val scaEnabled: Boolean,
                              @JsonProperty("team_ids") val teamIds: List<Long>?,
                              @JsonProperty("policy_legacy_id") val policyLegacyId: Long?,
                              @JsonProperty("policy_version") val policyVersion: Int?,
                              @JsonProperty("scan_name") val scanName: String?,
                              @JsonProperty("submitted_by") val submittedBy: Int?,
                              @JsonProperty("submitter_name") val submitterName: String?)
