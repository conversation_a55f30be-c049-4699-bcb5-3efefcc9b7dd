changelog:
  image: ruby:2.6
  stage: changelog
  tags:
    - sourceclear
  before_script:
    - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client -y )'
    - 'which git || ( apt-get update -y && apt-get install git -y )'

    - git checkout master
    # gitlab-runner by default clones via https, however we will commit changes
    # via ssh independently with `git push`. If not set the remote url, 403 is
    # returned when pushing.
    - URL_WITHOUT_HTTPS=`echo $CI_REPOSITORY_URL | sed 's/^https:.*@//' -`
    - REMOTE_SSH_URL=`echo $URL_WITHOUT_HTTPS | sed 's/\//:/' -`
    - git remote set-url origin git@$REMOTE_SSH_URL

    - eval $(ssh-agent -s)
    - echo "$SSH_PRIVATE_KEY" | base64 --decode | ssh-add - > /dev/null
    - mkdir -p ~/.ssh
    - chmod 700 ~/.ssh
    - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" > ~/.ssh/config
    - git config --global user.email "<EMAIL>"
    - git config --global user.name "Hedwig"
    - git checkout -B "$CI_COMMIT_REF_NAME"
  script:
    - ruby bin/generate-changelog
  only:
    - master
  when: on_success
