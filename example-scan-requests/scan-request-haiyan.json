{"header": {"id": "c5cc0bcd-0a63-4f7c-aa31-0967b138d202", "send_time": *************, "send_timestamp": "2019-03-04T19:02:00.952Z", "receive_time": *************, "topic_name": "", "originating_application_id": "sca-scanconfig-service", "microservice_id": "sca-scanconfig-service", "environment": "", "security_context": {}}, "principal": null, "context_id": null, "metadata": {}, "content_type": "JSON", "content": "eyJzY2FuX2NvbmZpZ19pZCI6IjUyMTJhNzNhLTQwNjMtNGNhNi05NjVhLWM3YmRjYWI3NTQ4NiIsIm1lc3NhZ2UiOiJ7XCJlbmNyeXB0X2l2XCI6XCJhYmNkZWZncWFhcVwiLFwiYWNjb3VudF9pZFwiOjEzMzA0LFwiZW5jcnlwdF9rZXlcIjpcInh5emFiY2FhYWFlaVwiLFwiYmluYXJ5X3BhdGhcIjpcImFjYy0xMzMwNFxcXC9hcHAxMDAwNDBcXFwvNTIxMmE3M2EtNDA2My00Y2E2LTk2NWEtYzdiZGNhYjc1NDg2XFxcL2JpbmFyaWVzXFxcL1wiLFwiZW5jcnlwdGVkX2tleVwiOlwiYWFhYWFhYVwiLFwiYnVja2V0X25hbWVcIjpcInNjYS1ub25wcm9kLWJpbmFyeS1zdG9yZS1tYXN0ZXJcIixcInNjYW5faWRcIjpcImVlMDAxNDQzLWZlOGUtNGQ1OS04N2ExLTYzMmVmMzAwY2NhMVwiLFwiZW52XCI6XCJ0ZXN0LWVudi0xXCIsXCJhcHBfaWRcIjoxMDAwNDMsXCJhcHBfdmVyX2lkXCI6MjAwMDQzLFwic2NhbkNvbmZpZ19pZFwiOlwiNTIxMmE3M2EtNDA2My00Y2E2LTk2NWEtYzdiZGNhYjc1NDg2XCJ9In0=", "actor": {"name": "SYSTEM", "guid": "", "legacy_id": "", "type": "SYSTEM", "internal": true, "organization_guid": "", "organization_name": ""}, "action": null, "resource": null, "audit": false}