package com.sourceclear.agent.wrapper.sourceclear.client

import com.fasterxml.jackson.databind.ObjectMapper
import com.github.kittinunf.result.Result
import com.sourceclear.agent.wrapper.http.GenericHttpError
import com.sourceclear.agent.wrapper.http.HttpError
import com.sourceclear.agent.wrapper.http.Non2XXError
import com.sourceclear.agent.wrapper.http.postJson
import com.sourceclear.agent.wrapper.http.send
import com.sourceclear.api.data.match.VeracodeScan
import io.jsonwebtoken.Jwts
import org.slf4j.LoggerFactory
import java.net.URI
import java.net.http.HttpClient
import java.net.http.HttpRequest
import java.security.KeyFactory
import java.security.spec.PKCS8EncodedKeySpec
import java.time.Duration
import java.time.Instant
import java.util.Base64
import java.util.Date
import java.util.UUID
import kotlin.math.pow

class SourceClearClient(private val baseURI: URI, signingKeyPEM: String) {

  private val objectMapper = ObjectMapper()

  private val keyFactory = KeyFactory.getInstance("RSA")

  private val ks = PKCS8EncodedKeySpec(Base64.getDecoder().decode(encodedPKCS8FromPem(signingKeyPEM)))

  private val LOGGER = LoggerFactory.getLogger(SourceClearClient::class.java)

  private val httpClient = HttpClient.newBuilder()
      .connectTimeout(Duration.ofMinutes(2))
      .build()

  fun veracodeScan(veracodeScanPayload: VeracodeScan, retries: Int): Result<String, HttpError> = veracodeScan(veracodeScanPayload, retries, retries)

  private fun veracodeScan(veracodeScanPayload: VeracodeScan, retries: Int, total: Int): Result<String, HttpError> {
    return when (val result = veracodeScan(veracodeScanPayload)) {
      is Result.Failure -> {
        val scanId = veracodeScanPayload.scanId
        if (retries == 0) {
          LOGGER.error("scanId: $scanId, Unable to contact SourceClear after $total tries: ", result.error)
          result
        } else {
          when (val err = result.error) {
            is Non2XXError -> LOGGER.error("scanId: $scanId, SourceClear API returned unexpected HTTP code ${err.statusCode} (${err.body})... retry ${(total - retries) + 1} of $total")
            is GenericHttpError -> LOGGER.error("scanId: $scanId, Generic connection error occurred: (${err.exception.message})... retry ${(total - retries) + 1} of $total")
          }
          // difference between retries and total is 0, backoff is 5 seconds
          // difference between retries and total is 1, backoff is 25 seconds
          // difference between retries and total is 2, backoff is 125 seconds
          val sleepTime =  5.0f.pow(total - retries + 1).times(1000).toLong()
          LOGGER.debug("Sleeping for {}", sleepTime.div(1000))
          Thread.sleep(sleepTime)
          veracodeScan(veracodeScanPayload, retries - 1, total)
        }
      }
      is Result.Success -> {
        result
      }
    }
  }

  fun veracodeScan(veracodeScanPayload: VeracodeScan): Result<String, HttpError> {
    val payload = objectMapper.writeValueAsString(veracodeScanPayload)

    LOGGER.debug("scanId: ${veracodeScanPayload.scanId}, Sending payload to sourceclear platform:\n $payload")

    val token = Jwts.builder()
        .setIssuer("SourceClear/Veracode Cloud Agent")
        .setIssuedAt(Date.from(Instant.now()))
        .setId(UUID.randomUUID().toString())
        .claim("authorities", "ROLE_VERACODE_CLOUD_AGENT")
        .signWith(keyFactory.generatePrivate(ks))

    val uri = baseURI.resolve(URI.create("/v1/veracode-scan"))

    val request = HttpRequest.newBuilder()
        .uri(uri)
        .postJson(payload)
        .timeout(Duration.ofMinutes(10))
        .header("Authorization", "Bearer " + token.compact())
        .build()

    return httpClient.send(request)
  }


  companion object {
    fun encodedPKCS8FromPem(signingKeyPEM: String) =
        signingKeyPEM.split("\n")
            .filter { !it.startsWith("---") }
            .joinToString("")
  }
}

