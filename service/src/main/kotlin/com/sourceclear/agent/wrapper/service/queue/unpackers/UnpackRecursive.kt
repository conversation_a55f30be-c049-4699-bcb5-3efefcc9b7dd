package com.sourceclear.agent.wrapper.service.queue.unpackers

import com.google.common.annotations.VisibleForTesting
import com.sourceclear.agent.wrapper.service.queue.QueueHandler.Companion.determineUnpacker
import org.slf4j.LoggerFactory
import java.io.EOFException
import java.nio.file.Files
import java.nio.file.InvalidPathException
import java.nio.file.Path
import java.util.stream.Collectors
import java.util.stream.Stream
import java.util.zip.ZipException

const val UNPACK_DIR_SUFFIX = "_veracode_unpack"

const val UNPACK_DELIMITER = "#zip:"

class UnpackRecursive {

  companion object {

    private val LOGGER = LoggerFactory.getLogger(UnpackRecursive::class.java)

    /**
     * Unpacks archives in a directory recursively in a breadth first manner.
     *
     * @param dir The directory to look for archives recursively
     * @param nestingLevel The nesting level of archives.
     * E.g. if we want to unpack a zip file that's in another zip file, the nesting level should be at least 1
     */
    fun unpackRecursively(dir: Path, nestingLevel: Int = 1, maxDepth: Int) {
      val archives = findArchives(dir, maxDepth)
      val queue = ArrayDeque<Pair<Path, Int>>(archives.map { it to nestingLevel }.collect(Collectors.toList()))

      while (queue.isNotEmpty()) {
        val (p, level) = queue.removeFirst()
        if (level == 0 || !Files.exists(p)) continue
        val target = p.parent.resolve(unpackDirectoryName(p.fileName.toString()))
        // check if we have been here, mainly for non-archived binaries
        if (!Files.exists(target)) {
          Files.createDirectory(target)
          // we checked above already
          if (unpackCheckInvalidPath(determineUnpacker(p, Unpacker.recursiveUnpackers)!!, p, into = target)) {
            queue.addAll(findArchives(target, maxDepth).map { it to level - 1 }.collect(Collectors.toList()))
          }
        }
      }
    }

    private fun unpackCheckInvalidPath(unpacker: Unpacker, file: Path, into: Path): Boolean {
      try {
        unpacker.unpack(file, into);
        return true
      } catch (exception: Exception) {
        when (exception) {
          is InvalidPathException,
            is ZipException,
            is EOFException -> {
              // Failure to unpack due to, for example, invalid path in the zip
              LOGGER.warn("Skipped unpacking {} due to error", file, exception)
            } else -> throw exception
        }
      }
      return false
    }

    private fun findArchives(dir: Path, maxDepth: Int): Stream<Path> = Files.find(dir,
        maxDepth,
        { path: Path, _ -> !Files.isDirectory(path) &&
            determineUnpacker(path, Unpacker.recursiveUnpackers) != null })

    @VisibleForTesting
    fun unpackDirectoryName(filename: String): String = filename + UNPACK_DIR_SUFFIX

  }
}
