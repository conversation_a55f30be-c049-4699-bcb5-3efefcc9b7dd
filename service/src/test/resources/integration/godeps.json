[{"evidenceId": "c8e9d15e-29ef-4da7-8eed-9881c8f3af9f", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 16, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L16"}], "coordinates": {"coordinate1": "github.com/google/go-github", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "27c7c32b6d369610435bd2ad7b4d8554f235eb01", "evidenceType": "COMMIT"}, {"evidenceId": "8723e8b0-96c6-40fc-8a61-311f261ceefe", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 56, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L56"}], "coordinates": {"coordinate1": "github.com/square/go-jose", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "7f0dd807d3f3d73bb536898cb7980ddf638ce69a", "evidenceType": "COMMIT"}, {"evidenceId": "8895060a-31fe-403f-90b3-6428f6fddf6f", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 20, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L20"}], "coordinates": {"coordinate1": "github.com/google/go-querystring", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "53e6ce116135b80d037921a7fdd5138cf32d7a8a", "evidenceType": "COMMIT"}, {"evidenceId": "4341b6f8-d521-417c-aba4-6980ff4b41fa", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 51, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L51"}], "coordinates": {"coordinate1": "github.com/simeji/jid", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "9090f06e9643aa1a4e95904a4ebbd6d186b3a20f", "evidenceType": "COMMIT"}, {"evidenceId": "c4c9d19c-039d-4ffd-84c4-cb69eb44ab54", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 11, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L11"}], "coordinates": {"coordinate1": "github.com/fatih/color", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "42c364ba490082e4815b5222728711b3440603eb", "evidenceType": "COMMIT"}, {"evidenceId": "d0052e53-685f-49a0-8059-cd22c9988b51", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 33, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L33"}], "coordinates": {"coordinate1": "github.com/mattn/go-runewidth", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "14207d285c6c197daabb5c9793d63e7af9ab2d50", "evidenceType": "COMMIT"}, {"evidenceId": "96ce2fac-c65b-49ad-bd5e-2be670b1f581", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 38, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L38"}], "coordinates": {"coordinate1": "github.com/nsf/termbox-go", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "abe82ce5fb7a42fbd6784a5ceb71aff977e09ed8", "evidenceType": "COMMIT"}, {"evidenceId": "744a41f2-78df-41b5-bc20-7092997b3ba4", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 68, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L68"}], "coordinates": {"coordinate1": "golang.org/x/sys", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "d75a52659825e75fff6158388dddc6a5b04f9ba5", "evidenceType": "COMMIT"}, {"evidenceId": "4238f49b-5e9c-44c2-917b-1bdc37463a7b", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 46, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L46"}], "coordinates": {"coordinate1": "github.com/pkg/errors", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "248dadf4e9068a0b3e79f02ed0a610d935de5302", "evidenceType": "COMMIT"}, {"evidenceId": "9b154c47-0155-483c-a668-acbe03fe5657", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 29, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L29"}], "coordinates": {"coordinate1": "github.com/mattn/go-isatty", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "30a891c33c7cde7b02a981314b4228ec99380cca", "evidenceType": "COMMIT"}, {"evidenceId": "916a1781-0264-4d7c-8e29-e63e798de440", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 6, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L6"}], "coordinates": {"coordinate1": "github.com/bitly/go-simplejson", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "da1a8928f709389522c8023062a3739f3b4af419", "evidenceType": "COMMIT"}, {"evidenceId": "c746bf23-ecb6-421e-a59f-5f99dd2e81cf", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 42, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L42"}], "coordinates": {"coordinate1": "github.com/nwidger/jsoncolor", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "0192e84d44af834c3a90c8a17bf670483b91ad5a", "evidenceType": "COMMIT"}, {"evidenceId": "a43125cb-d0b4-47f7-b80a-b7a99d71a8b0", "evidencePaths": [{"filePath": "godeps.zip#zip:Godeps/Godeps.json", "lineNumber": 24, "dependencyPath": [], "filePathWithLineNumber": "godeps.zip#zip:Godeps/Godeps.json#L24"}], "coordinates": {"coordinate1": "github.com/mattn/go-colorable", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "d228849504861217f796da67fae4f6e347643f15", "evidenceType": "COMMIT"}]