[{"evidenceId": "14f9f7dd-c401-4303-9925-9c94b3fcfa14", "evidencePaths": [{"filePath": "a2.zip#zip:a2/a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar:::org/apache/commons/fileupload/portlet", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "90bad5e0fc0193236dddaafbd38b246c27f6c26542f625dd52fe56732279915a", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "da211229-231a-49c1-a310-c6dd78f70885", "evidencePaths": [{"filePath": "a2.zip#zip:a2/a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar:::org/apache/commons/fileupload/disk", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "895b1f850c413a7a9206c399770575216e4686aabc359d6b46511f444684d5a6", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "231080f6-20f0-4af2-81a8-6f098d9acfed", "evidencePaths": [{"filePath": "a2.zip#zip:a2/a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": "a10c06183fe21f3bb3dda3b5946b93db6e2ad5cc", "sha2": "b570cacc936c8b6ed4b7741219de8782a6a796c6f929e97625a888847a8df1f3", "bytecodeHash": "d3a1682c87cb8c88325695e995b400255c5e526e6fb4fa335b4b7ed27f8d73c9", "commitHash": null, "evidenceType": "JAR"}, {"evidenceId": "a1263125-b1d7-42d6-964f-523cbb923c72", "evidencePaths": [{"filePath": "a2.zip#zip:a2/a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar:::org/apache/commons/fileupload/servlet", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "dee850e219c97c8a3c48d8ec4bf36eda7053e9ecf5b1d0008105270da3b47af1", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "27f2b958-75bb-4611-9486-aace6c50c370", "evidencePaths": [{"filePath": "a2.zip#zip:a2/a1/commons-fileupload-1.2.zip#zip:commons-fileupload-1.2.jar:::org/apache/commons/fileupload/util", "lineNumber": null, "dependencyPath": []}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "a752b148f6e549741cb46b2c851d0cf5bc6b20205664ce218b88958004fdd9e7", "commitHash": null, "evidenceType": "BYTECODE"}]