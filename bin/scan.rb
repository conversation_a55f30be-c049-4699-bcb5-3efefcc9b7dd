require 'base64'
require 'json'
require 'optparse'
require 'securerandom'
require 'tempfile'
require 'find'
require 'fileutils'

options = {}
OptionParser.new do |opts|
  opts.banner = "Encrypts and uploads a binary to be scanned by an agent wrapper instance\n"\
    "This script assumes that your AWS credentials are set in ~/.aws/credentials\n"\
    "and it requires the awscli and openssl installed\n"\
    'Usage: scan.rb [options] file'\
    "\n\n"\
    "Example:\n"\
    "\n"\
    '    ruby bin/scan.rb -q jsyeo-scan-request -b jyeo-test -k alias/sca-nonprod-dev-master-key ~/repos/example-ruby.zip'

  opts.on('-pPROFILE', '--profile=PROFILE', 'AWS profile to use. If not specified, the default profile is used.') do |p|
    options[:profile] = p
  end

  opts.on('-qQUEUE', '--queue=QUEUE', 'AWS sqs queue to send message to') do |q|
    options[:queue] = q
  end

  opts.on('-bBUCKET', '--bucket=BUCKET', 'The AWS bucket to upload our binaries to') do |b|
    options[:bucket] = b
  end

  opts.on('-kKEY', '--key=KEY', 'The AWS KMS key id, alias, or arn to encrypt our encryption keys with') do |k|
    options[:key] = k
  end

  opts.on('-h', '--help', 'Prints this help') do
    puts opts
    exit
  end
end.parse!

defaults = {
  queue: 'jsyeo-scan-request',
  bucket: 'jyeo-test',
  key: 'alias/sca-nonprod-dev-master-key'
}
if !options[:queue]
  puts "WARN: defaulting to the #{defaults[:queue]} SQS queue" 
  options[:queue] = defaults[:queue]
end
if !options[:bucket]
  puts "WARN: defaulting to the #{defaults[:bucket]} S3 bucket for uploads" 
  options[:bucket] = defaults[:bucket]
end
if !options[:key]
  puts "WARN: defaulting to the #{defaults[:key]} KMS master key to encrypt encryption keys and ivs" 
  options[:key] = defaults[:key]
end
profile_opt = if !options[:profile]
                puts "WARN: defaulting to default profile in your AWS config" 
                ''
              else
                "--profile #{options[:profile]}"
              end

def bin_to_hex(b)
  b.unpack('H*').first
end

def hex_to_bin(s)
  s.scan(/../).map { |x| x.hex }.pack('c*')
end

def base64decode(s)
  Base64.decode64(s)
end

binary_path = "acc-15665/#{SecureRandom.uuid}/binaries/"
s3_path = "s3://#{options[:bucket]}/#{binary_path}"

# Generate our key and iv
out = `openssl enc -nosalt -aes-256-cbc -k lol -P`
# Prints:
#   key=cafebabe
#   iv =lolfoobarbaz
encrypt_keys = out.lines.each_with_object(Hash.new(0)) do |line, accum|
  splitted = line.strip.split('=').map(&:strip)
  accum[splitted[0]] = splitted[1]
end

filename = File.absolute_path(ARGV[0])
if File.directory?(filename)
  puts "Encrypting folder..."
  Dir.mktmpdir("encrypted") do |temp|
    Find.find(filename) do | file |
      if File.file?(file)
        tmp_file = File.join(temp, file.delete_prefix(filename))
        FileUtils.mkdir_p(File.dirname(tmp_file))
        puts "Encrypting file: #{file}"
        `openssl enc -aes-256-cbc -K #{encrypt_keys['key']} -iv #{encrypt_keys['iv']} -in #{file} -out #{tmp_file}.aes`
      end
    end
    puts "Uploading encypted files in folder, #{filename}, to s3..."
    puts `aws s3 cp #{profile_opt} #{temp} #{s3_path}#{File.basename(filename)} --recursive`
  end
  puts "Successfully uploaded encrypted folder, #{filename}"
else
  Tempfile.create 'encrypted' do |temp|
    puts "Encrypting binary..."
    `openssl enc -aes-256-cbc -K #{encrypt_keys['key']} -iv #{encrypt_keys['iv']} -in #{filename} -out #{temp.path}`
    puts "Uploading encypted binary to s3..."
    puts `aws s3 cp #{profile_opt} #{temp.path} #{s3_path}#{File.basename(filename)}.aes`
    puts "Successfully uploaded encrypted binary, #{filename}"
  end
end

# Encrypt key and iv with aws kms
Tempfile.create 'encrypted_keys' do |temp|
  # Sigh... because the smart people at veracode platform decided to smash these two strings together
  # See https://wiki.veracode.local/display/Eng/Static+Scans+AWS+KMS+Based+Encryption+Support
  keys = hex_to_bin(encrypt_keys['key'] + encrypt_keys['iv'])
  temp.write(keys)
  temp.flush()
  out = `aws kms encrypt #{profile_opt} --key-id alias/sca-nonprod-dev-master-key --plaintext fileb://#{temp.path}`
end

encrypt_key = bin_to_hex(base64decode(JSON.load(out)["CiphertextBlob"]))

data = {
  "encrypt_iv": nil,
  "encrypt_key": nil,
  "account_id": 15665,
  "binary_path": binary_path,
  "encrypted_key": encrypt_key,
  "bucket_name": options[:bucket],
  "scan_id":"ded6831d-e965-453f-911a-0d3def24152a",
  "env":"qa-18",
  "app_id":54378,
  "app_ver_id":53991,
  "scan_config_id":"1c5d6830-452d-46d7-8205-87b67d5d0bc7"
}
queue_url = JSON.load(`aws sqs get-queue-url --queue-name #{options[:queue]}`)['QueueUrl']
puts "Putting message on the #{queue_url} queue..."
`aws sqs send-message --queue #{queue_url} --message-body '#{data.to_json}'`
puts 'Successfully sent sqs message to trigger scan'
