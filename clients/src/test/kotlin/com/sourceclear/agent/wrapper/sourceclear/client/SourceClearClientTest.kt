package com.sourceclear.agent.wrapper.sourceclear.client

import org.junit.Test
import kotlin.test.assertFalse
import kotlin.test.assertTrue

class SourceClearClientTest {
  @Test
  fun encodedPKCS8FromPemTest() {
    val testPem = "-----BEGIN PRIVATE KEY-----\n" +
        "FOOBARFOOBAR\n" +
        "FOOBARFOOBAR\n" +
        "FOOBARFOOBAR\n" +
        "FOOBARFOOBAR\n" +
        "-----END PRIVATE KEY-----"
    val pkcs8 = SourceClearClient.encodedPKCS8FromPem(testPem)
    assertFalse { pkcs8.contains("\n") }
    assertFalse { pkcs8.contains(Regex("\\s")) }
    assertFalse { pkcs8.contains("-----BEGIN PRIVATE KEY-----") }
    assertFalse { pkcs8.contains("-----END PRIVATE KEY-----") }
    assertTrue { pkcs8 == "FOOBARFOOBARFOOBARFOOBARFOOBARFOOBARFOOBARFOOBAR" }
  }
}
