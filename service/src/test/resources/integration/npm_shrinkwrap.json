[{"evidenceId": "1977eaaf-2108-4f7f-9d65-84e8d8aa076f", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "debug", "coordinate2": null, "version": "0.8.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "96e9a4e1-e8ed-4eac-af57-2de8f4084162", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "qs", "coordinate2": null, "version": "0.6.6", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "731e85e0-ddad-42ae-8233-165a43964d77", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "range-parser", "coordinate2": null, "version": "1.0.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "485cb7d2-7b33-4db0-ac68-0e9b4218bae3", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "debug", "coordinate2": null, "version": "0.8.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "46e066eb-ea29-400f-af18-f7ca105fcbd1", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "path-to-regexp", "coordinate2": null, "version": "0.1.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "5ac45e4f-5c47-45f5-8bb5-d40fc182c58f", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "accepts", "coordinate2": null, "version": "1.0.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "1e296bc7-5b5c-4e7a-ac7f-ed052fbb46df", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "utils-merge", "coordinate2": null, "version": "1.0.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "dc6fc0f6-309c-4ebc-86c3-c976ff73e46d", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "j<PERSON>y", "coordinate2": null, "version": "3.0.0-alpha1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "92ffa8da-cd8e-4386-b332-a895bc7e0252", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "escape-html", "coordinate2": null, "version": "1.0.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "d1035bb7-bd14-441a-be2c-20fc4da780f6", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "send", "coordinate2": null, "version": "0.3.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "74cdb9de-2026-4bdf-a9ea-c8197ddeb812", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "negotiator", "coordinate2": null, "version": "0.4.9", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "1397a138-28ad-4cb5-b748-a7c5ac569d00", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "type-is", "coordinate2": null, "version": "1.1.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "66b24df5-9d25-4b37-a152-130e05e66e9c", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "fresh", "coordinate2": null, "version": "0.2.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "c110d2df-907b-4dc5-b47d-3cae26ce72df", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "cookie-signature", "coordinate2": null, "version": "1.0.3", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "6e5c5a25-e193-453b-9725-0a7d2350ee3b", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "express", "coordinate2": null, "version": "4.1.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "0e2562b0-1c97-4561-90a7-511a39ea06a1", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "mime", "coordinate2": null, "version": "1.2.11", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "26d28669-a986-44be-bbb9-4f65ab4d78a0", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "buffer-crc32", "coordinate2": null, "version": "0.2.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "de2eb89e-a7f9-46eb-9fb0-55b24c7de8f7", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "cookie", "coordinate2": null, "version": "0.1.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "d39bd7b0-c385-41b4-b94b-113fcb748547", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "angular", "coordinate2": null, "version": "1.3.19", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "08043840-acd5-457c-87e7-8a0ecaaa63c6", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "methods", "coordinate2": null, "version": "0.1.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "08e7a022-6204-42eb-aab3-5e1581f9e0b1", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "merge-descriptors", "coordinate2": null, "version": "0.0.2", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "76e8bc29-f17b-4212-b1cf-3d5bef368112", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "parseurl", "coordinate2": null, "version": "1.0.1", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}, {"evidenceId": "e87b71a4-2ac1-4b9e-ab31-8393fcbf91d2", "evidencePaths": [{"filePath": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "npm_shrinkwrap.zip#zip:npm-shrinkwrap.json"}], "coordinates": {"coordinate1": "serve-static", "coordinate2": null, "version": "1.1.0", "platform": null, "scope": null, "coordinateType": "NPM"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": null, "evidenceType": "COORDINATES"}]