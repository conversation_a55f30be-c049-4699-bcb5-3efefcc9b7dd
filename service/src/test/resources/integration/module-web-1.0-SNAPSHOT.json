[{"evidenceId": "a21d9a7d-a60e-421b-8d28-5a5801e8af52", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/xml", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/xml"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "886ee99fb99db225ee64f0e5cd992a85f458bb2b971e5f55d51af77f0a6ce363", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "386ce47d-05f9-4832-b106-01eb2a58963c", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/annotation", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/annotation"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "8e7c7e5a22cf5422510e17bc6f13d6601282e9075e4769dee45ed18f2c40409f", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "af2dcbc1-7a2b-4b0b-98b7-522a24e71ef2", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/annotations", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/annotations"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "542c3cd5c5fa054460149703833d4f1172f4b083eb8e591a9abb18ffaa05bc9c", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "dbd75f87-b42d-4d4c-a537-369a22fdaca2", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/cbor", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/cbor"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "c9aee81cc257be5e07a0d24a1fb31faeacaf048e6fa914d9aa7f47e0660995d1", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "5d1fb8fb-d328-4929-971d-29a3cbb6cb4b", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/bind/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/bind/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "0e8e74efe8bceb9d7dc5b4ccfb36bedee516d37220e0a679d2e74f24b79d8a9e", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "041733e6-da21-48d0-9741-f77c722fa938", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/type/filter", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/type/filter"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "3808610728c207cc947ae16ccb6f50c080c52034a1ecd2404bbc66b637794164", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "b9b18e82-ff13-4072-939e-e746c6c266b8", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/core", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/core"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "b41fef0e4c7d6c18e218e0eefd49f4ed661ea08e334e61d758e9dcd57d0c59b5", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "0bbb2ca6-0747-49d0-b523-e0bf6281a1f3", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "e16c1cfd69cd2abba5b6b93b39679e86097f0e73cc3fbe356531b396592974c6", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "a4f0026d-a9f0-4863-a1fe-e835595d53a3", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/method/annotation", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/method/annotation"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "a72753ca81d4fc3acf65a5a04410e905e8025048dc9973cc0827a14688ff4ff9", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "1c747519-f388-47ed-bb48-fe5ce79d5abf", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context/annotation", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context/annotation"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "e90ec7cbc9d207a5dc24c5df593833f0fe56219114ec360e441ef3c9da325eb5", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "d3c8ed08-34ca-433b-a3f4-2f62faf326f4", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/type", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/type"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "d35f6d1be7e5156b27fb1a0e6af0b0af8622ffea040da3030075889646ced9c2", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "79702f37-b005-4fe0-ac86-aef5c15ce164", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/task/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/task/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "7d5a04762b0e7f001b8ae1061a09e0e6ea2ca2645852919ff6ec453dc17c7a87", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "8e9bb69e-e87c-4c7d-bb52-20eb4da2ad07", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/convert", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/convert"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "24538c55321096913c1e543267ab62641e7696365561efa46d7bcf6da73d359b", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "d00395af-51dc-448f-856d-13984e6b1b7d", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "a24a17d29205f2ee74174644aa348ed654bfb576969d78b881e0861dd10efb3b", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "596285f5-9cc9-4f07-aa32-3816f6d6e1ae", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/transform", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/transform"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "073763fe71a98fdeab8b39890086d28696ec3e37e53de39181f68007a86b9f0b", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "961a77b3-5e24-4f8a-9c2f-34f447496d73", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/jsf", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/jsf"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "9c163124d4deb30d3759b2439d29791bcc06fa94708ab9efada16c2abe519dbc", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "3e3be515-1eb7-40fe-b64d-ba6dfd35210f", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/client/reactive", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/client/reactive"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "c12f884ab5e20985d8b787fe66d546df49123bcd4a50fd206adfb088c527c314", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "6696915a-c480-47c7-ad38-57779223ebf3", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/jbcrypt-0.3m.jar", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/jbcrypt-0.3m.jar"}], "coordinates": null, "sha1": "fe2d9c5f23767d681a7e38fc8986b812400ec583", "sha2": "c0717079f4fe18f72f36ad1ab15a2afa63c6544fee4b9ac2128851330b5e1031", "bytecodeHash": "8173bd4f50b9297285e9be2799dc481a5330d17eea7143a5990a9cc414fbbfe3", "commitHash": null, "evidenceType": "JAR"}, {"evidenceId": "39831a11-6173-41be-895d-a9dbde8cd7f1", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/remoting/caucho", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/remoting/caucho"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "fb73edcff5f9057c607b3ade0b92a38404d19c66b10354576306fe7e3d084dd3", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "88c230b1-8033-44f4-b814-1a91f220a5c3", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/filter/reactive", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/filter/reactive"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "fb6a352f7977c8a2d264da45ac55079e29fff9ad47469ef44bda7f9c84a43a61", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "cd246d9b-efc9-4f0f-8a95-ec068246fdb2", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/serializer/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/serializer/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "0407a5e1b191edbf2654ddcc26eead64892af648f39eb206ee2181ea7c35c706", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "1b763d2a-961f-44bf-8800-d319956dd4b7", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "0b38df18447c558eda9ac3cea94c9854f12d26e3f73e66dd00a2fa5ed619eeb6", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "e4539798-d3f7-4845-961f-76ff0c14b3a4", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/concurrent", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/concurrent"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "1e15107a2ae6aa23f2dcd8f5cec41832647da9c1c52c9b4eaa489c4d70e50e87", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "c263f844-31cb-44f6-9b44-3ec43f1f35e6", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar:::org/apache/commons/logging/impl", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar:::org/apache/commons/logging/impl"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "40df220c600147967485406880a2b74d2734dec9aa8131a086c5e69cfb3c5be7", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "3eafe09b-9c0c-4a9d-83a4-e27219c7daff", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war"}], "coordinates": null, "sha1": "7bdf86e87adbe9fb960eff5878477467fb061c51", "sha2": "ec32023289dc93971801510127e0b32367e35017c5bf2b041fc22e5c0ea64ed7", "bytecodeHash": "b5fd28bec04b081695654ff716ec67de2f74b4862eb9d6ea67725d1d0b19b90f", "commitHash": null, "evidenceType": "JAR"}, {"evidenceId": "0bd4a4fa-5871-4675-a30e-de9bbe5ec1c5", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/lang", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/lang"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "74d0a6c0bdab1f47cd0c133c6a0adf4442c787fd860a8b0c14246e537bc184ce", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "7c7d34da-5088-4e08-9a48-c166406f86b7", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/transform/impl", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/transform/impl"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "4e713fc246b23dc00bef88738dd231b4e38c0411be5505b02d601c314405eae9", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "82f7c8df-b559-469f-8dfc-a0652d3ec903", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/io/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/io/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "6e709bd06c5fed3c31bdf028587b1232b59aae87ed5ff4cd34a80d6b5e619c95", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "616064b1-b6da-4750-9af1-f5f5a6d49e21", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/remoting/jaxws", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/remoting/jaxws"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "f0a91685b1107c71930ec2f7a1193c15a86bd0c8e02d4f04ea1c11e180c7e7ca", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "458ccb0c-59cf-454a-a0e6-966bd2a1b464", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context/request", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context/request"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "ecb9efa95dd287a56b1088768cfd9a04e53f60a19e73bf207f14e23b76c74755", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "919988cf-eccc-4fda-bb96-908e2a0c111e", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/core/internal", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/core/internal"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "bbb9642c772b7124f387826a96f5e3545eae2fbff1be6b9ce09a9e7ea7c014e7", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "24958b94-6995-4ae9-a054-1032efd51ccb", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/io/buffer", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/io/buffer"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "59e5ceb7c18fe53c16cbe515b73e68858bef1b7d3346b6a92d84676229743fc6", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "e214f3ba-941e-40e8-9c61-d3ed0f8e1b83", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/method", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/method"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "3f83c163c8812371110026c6204c02bcfbeacd8ed359a488aadae27966f13e30", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "315f9a44-5f59-4e4a-ab99-562aac7560a4", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/codec", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/codec"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "55e4c0de18b65f9a444b6cabffa9824cc7a205d08d961700235c0c6a07b670c0", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "1315c41d-f0cf-4242-a720-bf46dbf9850b", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/xml", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/xml"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "1c69c2fc785e77003d09cfed3322b21eafec3953afe3d74883acb764d0a35f96", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "d6f6e306-05d1-43d3-8b47-1cb1d6d3fddb", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar"}], "coordinates": null, "sha1": "b9b00d4075c92761cfd4e527e0bdce1931b4f3dc", "sha2": "3f646f7a51bd3a32c89241b899f6cc73dc40ea8275cd3233f4699668bfb839c5", "bytecodeHash": "547c8943e51fb6201dade9ab42f2245e149c714272a5c37e2c53c424f733fb98", "commitHash": null, "evidenceType": "JAR"}, {"evidenceId": "43ae2d42-9b23-493e-9b57-8c6166826000", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "da99c49aadb7f284fd6d3e713c6c868c349cd357581124fb80c6939d7dcfec58", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "78fb0793-27f7-4328-a0e4-74646bc77721", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/client/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/client/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "39936a36ecfedf6b922e2827735ad6747e69cacb6644deb7413ab13e1c9f2e37", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "eb4a12f5-8080-42e3-b971-18bfcdded33e", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/serviceloader", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/serviceloader"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "546eb5ea700c272e545d0709c7e1957d4ff170a4408154b36c845c5ffed7c459", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "05a547e0-8008-47b7-b768-52fc7633ada5", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "e04f60233cc2930dccf2d57f2cc420fd8de5286d30ce698369a05c46e2ea5af2", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "770bc450-7502-4177-b852-ac08aa2defaa", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/util/pattern", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/util/pattern"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "4b956eed0d0a168ed8f35313b637de37c0211491b41367863474e673b936fc30", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "6065dcab-df46-4a1d-ab2b-02c26f56ca27", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/annotation", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/annotation"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "ac342400b7780256dadb1ca0f95950dfdbe1aae6209f1130efc4c99a0722308d", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "122ce430-fa92-4764-9ab3-e4808a6c5561", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/method/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/method/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "b0d75f70f215cde15fe962c7a88ebbac12e55c8eac44f880a8e9d789a1e6c951", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "179d8107-ba4f-4362-9a62-15d1c524cfe9", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/protobuf", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/protobuf"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "45146aa19b325c5564d3226d76d00a84bf86286a2ecf0be3febe6b92524ef2b5", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "683adf89-4e1c-41d3-826a-73ac68264d20", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/propertyeditors", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/propertyeditors"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "454cfffbd05b020b2b7a7985917c9317025e4fc9edc1d30a2bd94baf32ec468b", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "56dc9ee2-b0a9-43cd-8c05-33751d6b05d5", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/feed", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/feed"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "a4eaaf10d0ba7842fba5d9d1939e27d08a917dfed95d16aa62d2f40e847f6e2b", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "482b69fc-d572-4ddf-97b9-96f3eae7ffaa", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/bind", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/bind"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "3ca65f010870012b2299e6ba419e450ca53a3ee83dde0a91f43f66fa1bf1cdaf", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "372dd9ea-d176-4762-9220-951c4908b3c2", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/multipart/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/multipart/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "6d64348210bd457c4a33ff332fa4d11667baff146fa4bc90fcd533349638e831", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "b60fdec4-265c-456f-b4a1-f2c0ac38a48e", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server/i18n", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server/i18n"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "19f17f2990e84e763f6dffe6cea53cc3e926fd279d8b76bc4fe1663d2e5a3588", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "239757bb-d639-47e1-9fdc-545836cbad09", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/server", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/server"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "e886678e451df9ee20d70d934fdb18f798504840190c421e421101fe76356911", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "d5206ae8-20dd-47e0-917d-4eb863930608", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "5bb4d52823002301ab5d63fd37264ca747f63f2c9636cb8afebbc42902da2ef5", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "8bb7b798-b60f-4736-99a2-bc95fe0da8b4", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/wiring", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/wiring"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "7c0c2e77a1c06f2e8b2a1dffa94b25dbb971eeca56fadf8c2cd7801a441b6cca", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "8a5df871-7c02-4416-8da6-0a76db8fab11", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/filter", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/filter"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "e06d9add337867ca8741adf70d8c74e213c2100099743cd57a0467736e6a9502", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "ed513c29-3d06-4d85-89f2-2066d7c9add6", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/multipart/commons", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/multipart/commons"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "6b70dc40cc9735e58a226f4c035ec6530ddcf64b3d96c91c25230e850ec5cdef", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "53990405-7b36-48cf-bd92-2b9e1ce6f471", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/server/reactive", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/server/reactive"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "11e7f58e5fd95a48274aa7c773feb757efcf23b5bb09427a7a9cd6a1716702be", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "80d02cf6-10dc-4046-bef1-a377eefb421a", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server/adapter", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server/adapter"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "3c40f4d1a48b1fe1eb7c399608a477dccc928f80c0ea2d79c5664b94ca1f3c36", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "e5e63a94-4206-4b33-a3c6-6399b2105a88", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/type/classreading", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/type/classreading"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "95231725dc5f6b4d874097e740f09133b0cd26ef1e530eba3c6e8cbb42b90987", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "428470c2-d89d-49c5-907f-1fdd456f1a5c", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/util", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/util"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "8f7b20f1b5b6d8e643b87fd195d7178c452296ec20a6e51458e9b108e156f265", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "a7f8e31e-b473-41d3-b539-532ac6ce4ba9", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/unit", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/unit"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "672a03e7faa64960f436d58db7ef71fb713d18f983b739c1c01994841be9bdd8", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "7ecc14f3-cf48-496d-82d2-d6dfd7b336fa", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar"}], "coordinates": null, "sha1": "3ff2a93b072da42c3930225e3dceeabb0678eb0b", "sha2": "fc135b74d9aa285c4aa8d9b19e2367817a7cd2d2af53f726b3d1466de428f4b0", "bytecodeHash": "082e2034c8683a2fe226673c90a732969596267459d3e09b7758c1127748e0e2", "commitHash": null, "evidenceType": "JAR"}, {"evidenceId": "431ca198-d22f-45d5-a1f1-1ed168b3549e", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/basic", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/basic"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "2f1b413b60577ad5d99c6b5d2b80230736796cad80deab4b63eddc06b21b1cf7", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "2fbcb98b-6b19-4a66-84cf-37805cdc9e40", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "c090deb68f406464efee542c24814b1dd5215c86f0c976db0be174158e68ab21", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "e65c914e-bb5e-4d34-874b-82b6529b1991", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/config", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/config"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "1de36254c611664394c0cd7832fccc481ac591a35c5209d890ab72eeab40a2ab", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "0a9fe879-4209-4390-ba24-3d232d2c7544", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/io", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/io"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "3efb5fe19794bdd1a1114ee91a598b61bb70e7be3b1965e0524fbe2a677c92b6", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "7a8368af-7ef5-473e-a77b-43bf9e38eddb", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/remoting", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/remoting"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "316ba6ffc431401b1767373a55ace7e3539542fd6f358a44eb1b34229f46ff9b", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "4f376250-7e2a-4e19-8f49-8f6e406ebaaf", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/bind/annotation", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/bind/annotation"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "8268f8ca409496e00343cf86a378f9ab4d2f48fb8dc3ad67d36023b409eba419", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "8bb75bbc-765b-42c8-98ed-d5a49d300eb1", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/xml", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/xml"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "f884280fc257084841b825c9b911c1dc0ce125d27ccfd978c3715abe5f39a0ae", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "c89db887-d4da-4323-90b4-208211fab665", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/gcj", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/gcj"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "534225a5642dff7fca35800ffc2b149b9eb6661726b10c270e9d1dd99bb5e8fd", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "13546d70-7dc2-471a-acaf-28fe092667c1", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/remoting/httpinvoker", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/remoting/httpinvoker"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "02bffc5de0777381a60cf24a124c873e79f994e72faecef38b5546124079ceaa", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "5d717819-15c0-4d89-b7c7-ed64fb2aa40b", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "13cf9bb19e2ac0e8bcd47b2969b73376256cb777bfe30152eb0eefb2a2f06d79", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "b0b1be38-63b9-4fe2-90b8-f9e8ac0167c1", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "803d8cf1d88a06788e012618328136b5b21998f880bd0b8c90f621cda04a018a", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "533ae7c7-334c-4e38-a1c7-c8507ac54c4d", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/function", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/function"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "816c1e2c90bb6a938dd9eaeb7cf3c99df120ce57db078c752b059ebd8226d6c5", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "e82205c9-f7c6-4dd1-b254-997a12b8d477", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/task", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/task"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "e818e72c4211dadc8cac144926c39a87c10f8489b66df39d522ee7cb5bf2c630", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "67bd24d0-6eda-469e-8e9b-189502c7122a", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "b47372be4b58bd486b4cf34f0725fbf1498e4cc57259f407d59251da631048f7", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "f96e39dc-d2a5-4a8e-bd48-5a223f69662a", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/json"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "5848e90ba0f15263e694e50fd75a4265b763f36c89e93009dd360fea7b8cd7b4", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "8f405236-9cc7-48c0-a7c9-bae87717a87b", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/groovy", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/groovy"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "15c94232fd6e0ffd487b594fd0be0a76da78a0468c36e2c2375832ba72d95bf4", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "ff4c6046-713a-42af-a567-c25610bc6300", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar"}], "coordinates": null, "sha1": "f0d7165b6cfb90356da4f25b14a6437fdef1ec8a", "sha2": "857503050793c73c02ae77041e31d66936a4fd59beb97ee6c72fae2bab1993c2", "bytecodeHash": "ea567912ca10ca51b5bc699c6053175a6c058e351f6829eac939e6efdf9dab7f", "commitHash": null, "evidenceType": "JAR"}, {"evidenceId": "a7fa6c72-7628-4a20-8413-2a009b5b95d8", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "c9d65da1bc42ee62a7d4d15f583ac40229f07cf397dbef453aebcc480278d092", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "799876a0-0493-4e15-9f1e-28b6371111cc", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/util", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/util"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "0e9e1a69abc233b070b8d079bcc0e1f472c5f400f52c58047d52281c4a1e49de", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "85b404e9-9e1e-4bbc-abd5-7af569f09133", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server/session", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server/session"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "0c72c311ab5744d269c6abe6fc563ee5363e1319b3f7b0fb995aaa65ab26eed3", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "8bbc2948-d7c2-480f-b1fc-1fa5f27ebfff", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "f93c7bc8f8430202780b108b8cd307fd010c4c1cab948b323c6738a92fbafc5e", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "fa0aced2-f2a6-46b8-8115-4de7db37ff6b", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "53b299491fee84701fb77a0bdd050a9e81a07c754d43ad71e7e9833771920384", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "24c02f8c-970a-4631-8f8b-3e8fa0ecca1f", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/protobuf", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/protobuf"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "58919e08ea014a2015895f89a19cb8ddeb768ae0e6726d54acc90ac6901999f2", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "ca5a2368-08c1-4a77-959e-273aa11c4c01", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/perc", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/perc"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "d1c40c93cfaac61052dfd181479083b639a9370b8d78da0534ecd4d54960f735", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "f76f5c94-8512-4844-83bb-4ecea0dc564e", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/log", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/log"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "aa31f3b84f3dec9e81d5c49baf2ff88f33e764d32b778fc628288d0c9a9d6aeb", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "3c2006e9-c37a-4a93-9501-a2f911853782", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/multipart", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/multipart"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "220caad07fedea6809879ef2ed501545107d264571b183af0545b6c9cb7bb165", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "17bfff39-ca73-483b-b7a6-de09b8a89f58", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/cors", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/cors"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "54195e80c873383088cd6edb13c0cf780254a183002cc1c7dec20b6b7bf2cf34", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "3b874770-2c71-410e-9e4e-5316fde7cc2a", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/env", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/env"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "c9937c0d9966fc305ad9e59d6232d1b1d577054f5f89a507a378eb71cd3cd8a6", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "ac0bd20b-e0c2-4d30-b106-8f07adb4cb82", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server/handler", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/server/handler"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "11fed2cdc819c66a953bd91eebc471a17d7fab73a43d70b23ab655537a186781", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "1c345d7c-e0bf-4a27-b63a-3f85501a11f2", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/parsing", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/factory/parsing"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "031cfc675d2c115eaee61e4d615a52b8654e1b39ebc0db53bbed4120c5339bc4", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "47d55d06-c0a9-46a7-8e29-e93a1f2c053f", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "b45060ad0eb70b857e36a8ac7678377d5af45d928d10b61b4aace745f56ed121", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "c1bd4767-4bbc-4b74-b889-6798aecac708", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/annotation", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/annotation"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "4be46052c073bc24ff18cf44b99d5402bbdf63c7296dc83e9f0c4a9f437be0f2", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "1fce1a24-e3a0-4d7c-baf3-1cf81f51e057", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/xml", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/xml"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "4d11747ba92b360865cfee764b927d61ed398bf3c23b84295358678016a4ee0c", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "af2b7970-a9be-4bda-9b9c-e090a2f56f21", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/accept", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/accept"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "b309d838aa1f2d4931daa62130db013ce93bf00b6d5ea5ec7f56b5096146bb99", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "56cec2f1-a5ba-44ed-b7c5-afbaaf8abda1", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/cors/reactive", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/cors/reactive"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "b98eca790ea04eda31527569f3f07f4bc9d3f9b4b833e97103d02ae23e5e18a1", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "c4892bc2-716e-4581-b1c3-911169aeec35", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/proxy", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/proxy"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "524648ab79d60f56e2b387ae20aa3f385ee1079c1d9ed5178c23a9e3cab50fd8", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "c4d127e9-415b-478e-b7d9-bce3982ea099", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "a87894e0cfe5da69c41badfc20bc69e01540e34ffdef93c586023820ebd3a2af", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "67d6f428-112e-4fb9-bb3a-c3d88b706e7c", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/convert/converter", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/convert/converter"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "da05abb5dada59cb54439b75fda985778715071831d8a73c3a071b90edf1f34a", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "13332eba-46ed-4c57-93e4-37b7b45f962b", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/asm", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/asm"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "5138b1a0aefa6c58703281388dafdde50b7c35177079d88f9b1801c10b88e8b2", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "7b12efa7-c403-4ff5-a2fb-ac0f97270821", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/client/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/client/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "49c3581231c945ef0784ab5c43bd8e61997915a219844209e5700c00eb274714", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "20f80255-2e7e-4883-bbfe-131bd5c47d60", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/util", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/util"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "70dc1bf61c578105ff91515f979bd9ba9958c77a33408a80f9142e9037de0b77", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "9beac8a3-ed19-4e0f-94f9-b0992883d6da", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "88fb01736b5a45ab7268e7c6f0405a1409dc4b6442cb451da1b6ee2a200254b4", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "c860200f-0360-4004-b023-586c81272a92", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/jsf/el", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/jsf/el"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "66ccef8ad084e68d879114c26d2877bb6ebede37d220c44c3df10b7b9a0ee679", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "3934284f-e07b-49fb-845c-ce091322a9af", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/client", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/client"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "7bc0592f3f50b76d2c920b3a05a10ff8ec9ae16205e86fd52e36cd4feab7bf29", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "7f3f9eb3-70a2-4a73-8605-4682869d02e3", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar"}], "coordinates": null, "sha1": "5d513701a79c92f0549574f5170a05c4af7c893d", "sha2": "131737816184d8e772a6e3bbf46025de432a0cc7a2646fef8d63450ec9444033", "bytecodeHash": "bcb0ca8ce11e45f49a4fd60be492057cd8c2bc304c95d80803fd7df65528f938", "commitHash": null, "evidenceType": "JAR"}, {"evidenceId": "bea4b812-a5c3-4060-bcda-c08225853b9d", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "9f83b9fac81758f2f1c58f9eef3949d0eab1be9d879255f7c22dad49dcda79bb", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "ef846977-28d2-4488-b611-6472b06f1a74", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/beans", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/beans"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "8d53a26a5cd40b58fe55a4e886abbac76a7c3f67ef4ae2a9edd8cec8f992cde9", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "5719981f-7d21-4759-ae33-2b0a26915b24", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/json", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/json"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "aa47a8326a532375014b5f9a9e013ce1644fa31c4ae09816051ecd89b371be09", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "a3e821cc-487e-4958-8e1f-9850fd5dbb88", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/reflect", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/cglib/reflect"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "8ad32335227c70ed13e7e6fb29c13f677a45744b2e0585bb13610c9505bfd849", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "4e006472-12e6-4cc8-9990-315cda0833cd", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/comparator", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/comparator"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "eff6b82bb4ddd25ee63e8af2a1949bd5a89520cb84cf784123cd3c955314d959", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "94f4d8bd-1a20-4e80-8b2f-094a6bf22e5c", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/convert/support", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/convert/support"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "ba4e90e69a37b0ac49a91ed6b547cbe1a3e8d58332eab31e87f43b8ddf94454a", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "10cc1c5e-a0dc-419a-8b19-d3aba6aa0999", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/sun", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/sun"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "d23b54573511f96eba1d58203edcd49a9ab51e66db7dd15c2654d8695fff3f1e", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "42781cdf-785a-413a-bbf9-88cea23ee72f", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context/request/async", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/web/context/request/async"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "3ee2cea8dffab9397472f94fd6b2a68f4135f52b2593371ff8a4552b78211012", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "1d61b754-1074-4823-b788-c0a46c4db993", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/backoff", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/util/backoff"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "bf2284e9152283d120e89311ca08f87dd4a9a4f225a6c0b9cd22910192619630", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "312a2d36-4ec8-41f8-b436-d0b64de855b8", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/multipart", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/codec/multipart"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "f7dc8cb9cc75bcb6f117f27ecbc85c54c7d53acd394ccef8f2a4fdd8fd33ecdc", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "e351fced-8e0d-49e3-b54f-46b415bcd90c", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/client", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/client"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "f575e0bbfede989b50c54c7c8d677d1afe3cd7e292a4a1a4a2866d2c47debd79", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "b514f4fe-b848-4bcf-bab1-19714ef4fabb", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/android", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/instantiator/android"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "e06f530ee1236eb6b18577a8008f62e3baeae318a195946b66b7eed5ce0169a0", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "db635548-f9ce-4d8c-b354-84b2e3f1780a", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/smile", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-web-5.1.2.RELEASE.jar:::org/springframework/http/converter/smile"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "92f2b847039f923876da78f4825b46f7aeba0917b72d60cd1b97b10c8fe92565", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "18e3c86b-a3db-4d46-816d-73b75ae69160", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/strategy", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/objenesis/strategy"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "1138f8dc74af4b61c3dc981143e180c5f308b6adb5f9ebe95dbc206395944f1b", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "e7860df4-b8ba-4d56-a4a7-12904652e3a8", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/style", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/style"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "e6837394f71cca248a68c4a7d4beeea42013e63718c614f2205ab08900df6f8c", "commitHash": null, "evidenceType": "BYTECODE"}, {"evidenceId": "2f865bb3-5a0e-441c-aea0-e82e9440971f", "evidencePaths": [{"filePath": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/serializer", "lineNumber": null, "dependencyPath": [], "filePathWithLineNumber": "module-web-1.0-SNAPSHOT.war#zip:WEB-INF/lib/spring-core-5.1.2.RELEASE.jar:::org/springframework/core/serializer"}], "coordinates": null, "sha1": null, "sha2": null, "bytecodeHash": "1dac8c49c6bbc1241ac643877ca8e7173b87ec4ae493139a89bbb97f6ca85c04", "commitHash": null, "evidenceType": "BYTECODE"}]