package com.sourceclear.agent.wrapper.service.queue.unpackers

import com.j256.simplemagic.ContentInfo
import com.j256.simplemagic.ContentInfoUtil
import com.j256.simplemagic.ContentType
import com.sourceclear.agent.wrapper.service.utils.Constants
import com.sourceclear.agent.wrapper.service.utils.unTarGz
import com.sourceclear.agent.wrapper.service.utils.unZip
import com.sourceclear.agent.wrapper.service.utils.unZipJsInJar
import com.sourceclear.agent.wrapper.service.utils.unTar
import org.apache.commons.io.FilenameUtils
import org.codehaus.plexus.util.cli.CommandLineUtils
import org.codehaus.plexus.util.cli.Commandline
import org.codehaus.plexus.util.cli.WriterStreamConsumer
import org.slf4j.Logger
import org.slf4j.LoggerFactory
import java.io.OutputStreamWriter
import java.nio.file.FileSystems
import java.nio.file.Path
import java.nio.file.PathMatcher

interface Unpacker {
  fun contentType(): ContentType

  fun matcher(): PathMatcher

  /**
   * Unpacks file into a target directory
   */
  fun unpack(file: Path, into: Path): Path

  /**
   * Checks the stipulated content type and file extension of the unpacker.
   * Content type of the file will be determined by SimpleMagic.
   *
   * @param file
   * @return if the matcher can be applied to the file
   */
  fun matches(file: Path, logger: Logger): Boolean {
    var match: ContentInfo = ContentInfo.EMPTY_INFO;
    try {
      match = contentInfoUtil.findMatch(file.toFile())
    } catch (e: Throwable) {
      //we do NOT want to log this as warn, as it is expected (IOException, NPException, etc).
      //if SingleMagic cannot detect or failed to handle detecting the content type, we will just continue for next file,
      //no SCA Un-packer will be applied to this file
      logger.debug("SimpleMagic cannot detect or failed to handle detecting the content type of {}, use EMPTY_INFO instead", file);
    }

    return match?.let { it.contentType == contentType() && matcher().matches(file) } ?: false
  }

  companion object {
    val unpackers = listOf(GZipUnpacker.instance, JavaArchiveUnpacker.instance, ZipUnpacker.instance, ApkArchiveUnpacker.instance,
      AabArchiveUnpacker.instance, TarUnpacker.instance)
    val recursiveUnpackers = listOf(JsInJarUnpacker.instance, GZipUnpacker.instance, JavaArchiveUnpacker.instance,
      ZipUnpacker.instance, ApkArchiveUnpacker.instance, NugetUnpacker.instance, AabArchiveUnpacker.instance, TarUnpacker.instance)
    private val contentInfoUtil = ContentInfoUtil()
  }
}

/**
 * An unpacker that unpacks jar files for JS package files.
 */
class JsInJarUnpacker : Unpacker {
  override fun contentType(): ContentType = ContentType.ZIP // A jar is also a zip

  override fun matcher(): PathMatcher = FileSystems.getDefault().getPathMatcher("regex:.*\\.jar(\\.aes)?")

  override fun unpack(file: Path, into: Path): Path {
    LOGGER.info("JsInJarUnpacking {} into {}", file, into)
    try {
      unZipJsInJar(file, into)
    } catch (t: Throwable) {
      LOGGER.warn("Skipping unpacking {} because of {}", file, t.message)
    }
    return into
  }

  companion object {
    private val LOGGER = LoggerFactory.getLogger(JsInJarUnpacker::class.java)

    val instance = JsInJarUnpacker()
  }
}

class GZipUnpacker : Unpacker {
  override fun contentType(): ContentType = ContentType.GZIP

  override fun matcher(): PathMatcher = FileSystems.getDefault().getPathMatcher("regex:.*\\.(tar\\.gz|tgz)(\\.aes)?")

  override fun unpack(file: Path, into: Path): Path {
    LOGGER.info("UnTarGzipping {} into {}", file, into)
    unTarGz(file, into)
    // We do not collect these archive files as evidences better to clean them up now to save space.
    // Thus we delete the file to conserve disk space.
    file.toFile().delete()
    return into
  }

  companion object {
    private val LOGGER = LoggerFactory.getLogger(GZipUnpacker::class.java)

    val instance = GZipUnpacker()
  }
}


/**
 * Unpacker for tar files (examples: test.tar, test.tar.aes)
 */
class TarUnpacker : Unpacker {
  override fun contentType(): ContentType = ContentType.TAR

  override fun matcher(): PathMatcher = FileSystems.getDefault().getPathMatcher("regex:.*\\.(tar(?!.gz))(\\.aes)?")

  override fun unpack(file: Path, into: Path): Path {
    LOGGER.info("UnTar {} into {}", file, into)
    unTar(file, into)
    // We do not collect these archive files as evidences better to clean them up now to save space.
    // Thus we delete the file to conserve disk space.
    file.toFile().delete()
    return into
  }

  companion object {
    private val LOGGER = LoggerFactory.getLogger(TarUnpacker::class.java)

    val instance = TarUnpacker()
  }
}

/**
 * An unpacker that unpacks zip files. In the context of agent wrapper, we rbv files as zip files.
 * rbv files are created by the Veracode platform for Ruby applications using a custom rubygem.
 * .rbv files have same format as a .zip file
 */
class ZipUnpacker : Unpacker {
  override fun contentType(): ContentType = ContentType.ZIP

  override fun matcher(): PathMatcher = FileSystems.getDefault().getPathMatcher("regex:.*\\.(zip|ZIP|rbv|RBV)(\\.aes|\\.AES)?")

  override fun unpack(file: Path, into: Path): Path {
    LOGGER.info("Unzipping {} into {}", file, into)
    unZip(file, into)
    // We do not collect these archive files as evidences better to clean them up now to save space.
    // Thus we delete the file to conserve disk space.
    file.toFile().delete()
    return into
  }

  companion object {
    private val LOGGER = LoggerFactory.getLogger(ZipUnpacker::class.java)

    val instance = ZipUnpacker()
  }
}

class JavaArchiveUnpacker : Unpacker {
  override fun contentType(): ContentType = ContentType.ZIP

  override fun matcher(): PathMatcher = FileSystems.getDefault().getPathMatcher("regex:.*\\.(war|ear)(\\.aes)?")

  override fun unpack(file: Path, into: Path): Path {
    LOGGER.info("Unzipping {} into {}", file, into)
    unZip(file, into)
    return into
  }

  companion object {
    private val LOGGER = LoggerFactory.getLogger(JavaArchiveUnpacker::class.java)

    val instance = JavaArchiveUnpacker()
  }
}

class ApkArchiveUnpacker : Unpacker {
  override fun contentType(): ContentType = ContentType.ZIP

  override fun matcher(): PathMatcher = FileSystems.getDefault().getPathMatcher("regex:.*\\.(apk|aar)(\\.aes)?")

  override fun unpack(file: Path, into: Path): Path {
    LOGGER.info("Unzipping {} into {}", file, into)
    unZip(file, into)
    return into
  }

  companion object {
    private val LOGGER = LoggerFactory.getLogger(ApkArchiveUnpacker::class.java)

    val instance = ApkArchiveUnpacker()
  }
}

class AabArchiveUnpacker : Unpacker {
  override fun contentType(): ContentType = ContentType.ZIP

  override fun matcher(): PathMatcher = FileSystems.getDefault().getPathMatcher("regex:.*\\.(aab)(\\.aes)?")

  override fun unpack(file: Path, into: Path): Path {
    try {

      val baseName = FilenameUtils.getBaseName(file.toFile().absolutePath)
      val aabDir = file.parent

      val aabPath = file.toFile().absolutePath
      val apksPath = aabDir.resolve("$baseName.${Constants.APKS_EXTENSION}").toFile().absolutePath

      val bundletoolPath = System.getProperty(Constants.BUNDLETOOL_PATH_SYSTEM_PROP)
      LOGGER.info("Bundletool path {}", bundletoolPath)
      LOGGER.info("Building AAB - {} to APKs - {}", aabPath, apksPath)

      //example, java -jar ./bundletool-all.jar build-apks --verbose --override --mode=universal --bundle=./app-debug.aab --output=./app-debug.apks
      val buildArgs = arrayOf(
        "-jar", bundletoolPath, "build-apks", "--verbose", "--overwrite", "--mode=universal",
        "--bundle=${aabPath}", "--output=${apksPath}"
      )
      val buildCmd = Commandline()
      buildCmd.setWorkingDirectory(aabDir.toFile().absolutePath)
      buildCmd.executable = "java"
      buildArgs.forEach { arg -> buildCmd.createArg().setValue(arg) }
      val buildCmdSystemOut = WriterStreamConsumer(OutputStreamWriter(System.out))
      val buildCmdSystemErr = WriterStreamConsumer(OutputStreamWriter(System.out))
      val buildCmdReturnCode = CommandLineUtils.executeCommandLine(buildCmd, buildCmdSystemOut, buildCmdSystemErr, Constants.COMMAND_LINE_TIMEOUT)
      if (buildCmdReturnCode == 0) {
        LOGGER.info("Done building from AAB to APKs")
      } else {
        LOGGER.error("Failed to build from AAB to APKs")
        return into
      }

      LOGGER.info("Unpacking APKs {} to universal.apk", apksPath)
      unZip(aabDir.resolve("$baseName.${Constants.APKS_EXTENSION}"), aabDir)

      //Due to bug - https://issues.apache.org/jira/browse/COMPRESS-562
      //just call jar xvf to extract the zip
      //example, jar xvf ./universal.apk
      val universalApkPath = aabDir.resolve(Constants.UNIVERSAL_APK)
      LOGGER.info("Unzipping {} into {}", universalApkPath, into)
      val unpackCmd = Commandline()
      unpackCmd.workingDirectory = into.toFile()
      unpackCmd.executable = "jar"
      unpackCmd.createArg().setValue("xvf")
      unpackCmd.createArg().setValue(universalApkPath.toFile().absolutePath)

      val unpackCmdSystemOut = WriterStreamConsumer(OutputStreamWriter(System.out))
      val unpackCmdSystemErr = WriterStreamConsumer(OutputStreamWriter(System.out))

      val unpackCmdReturnCode = CommandLineUtils.executeCommandLine(unpackCmd, unpackCmdSystemOut, unpackCmdSystemErr, Constants.COMMAND_LINE_TIMEOUT)
      if (unpackCmdReturnCode == 0) {
        LOGGER.info("Done unpacking ${Constants.UNIVERSAL_APK}")
      } else {
        LOGGER.error("Failed to unpack ${Constants.UNIVERSAL_APK}")
        return into
      }

    } catch (e: Exception) {
      LOGGER.warn("Failed to unpack AAB", e)
    }

    return into
  }

  companion object {
    private val LOGGER = LoggerFactory.getLogger(AabArchiveUnpacker::class.java)

    val instance = AabArchiveUnpacker()
  }
}

class NugetUnpacker : Unpacker {

  override fun contentType(): ContentType {
    return ContentType.ZIP
  }

  override fun matcher(): PathMatcher {
    return FileSystems.getDefault().getPathMatcher("regex:.*\\.nupkg(\\.aes)?")
  }

  override fun unpack(file: Path, into: Path): Path {
    LOGGER.info("Unzipping {} into {}", file, into)

    // We unpack the content of the Nuget, but only the DLL files.
    unZip(file, into, ".dll")

    // We do not collect Nuget files as evidence, so we remove the file to conserve disk space.
    file.toFile().delete()

    return into
  }

  companion object {
    private val LOGGER = LoggerFactory.getLogger(GZipUnpacker::class.java)

    val instance = NugetUnpacker()
  }

}

