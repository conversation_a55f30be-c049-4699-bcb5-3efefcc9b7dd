package com.sourceclear.agent.wrapper.service.config

import com.sourceclear.agent.wrapper.scanconfig.client.Client
import com.sourceclear.agent.wrapper.scanconfig.client.TwoWayTLSPair
import org.springframework.boot.context.properties.ConfigurationProperties
import org.springframework.context.annotation.Bean
import org.springframework.stereotype.Component
import java.io.File
import java.security.KeyStore

@Component
@ConfigurationProperties(prefix = "scanconfig")
class SCAServiceConfig {
  lateinit var url: String

  lateinit var port: String

  lateinit var truststorePassword: String

  lateinit var truststorePath: String

  lateinit var keystorePassword: String

  lateinit var keystorePath: String

  @Bean
  fun scanconfigClient(): Client {
    val trustStore = KeyStore.getInstance(File(truststorePath), truststorePassword.toCharArray())
    val keystore = KeyStore.getInstance(File(keystorePath), keystorePassword.toCharArray())
    return Client(url, port.toInt(), TwoWayTLSPair(trustStore, keystore, keystorePassword = keystorePassword.toCharArray()))
  }
}
