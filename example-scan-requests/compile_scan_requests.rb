require 'json'
require 'base64'

src_dir = 'src'
out_dir = 'out'

Dir.foreach out_dir do |file|
  next if file == '.' || file == '..'
  File.delete(File.join(out_dir, file))
end

Dir.rmdir out_dir if File.exists? out_dir
Dir.mkdir out_dir

def to_serialized_message scan_request
  scan_config_id = scan_request['scan_config_id']

  scan_request.delete 'scan_config_id'

  new_scan_req = {
    encrypted_key: 'unknown',
    bucket_name: 'sca-nonprod-binary-store-master',
    scan_id: 'ee001443-fe8e-4d59-87a1-632ef300cca1',
    env: 'test-env-1',
    app_ver_id: 200043,
    scanConfig_id: scan_config_id
  }.merge(scan_request)

  str = new_scan_req.to_json

  content = {
    scan_config_id: scan_config_id,
    message: str
  }

  {
    header: {
      id: 'c5cc0bcd-0a63-4f7c-aa31-0967b138d202',
      send_time: 1551726120952,
      send_timestamp: '2019-03-04T19:02:00.952Z',
      receive_time: 1551726120953,
      topic_name: '',
      originating_application_id: 'sca-scanconfig-service',
      microservice_id: 'sca-scanconfig-service',
      environment: '',
      security_context: {}
    },
    principal: nil,
    context_id: nil,
    metadata: {},
    content_type: 'JSON',
    content: Base64.encode64(content.to_json),
    actor: {
      name: 'SYSTEM',
      guid: '',
      legacy_id: '',
      type: 'SYSTEM',
      internal: true,
      organization_guid: '',
      organization_name: ''
    },
    action: nil,
    resource: nil,
    audit: false
  }
end

Dir.foreach src_dir do |file|
  next if file == '.' || file == '..'
  scan_request = JSON.load(File.read(File.join(src_dir, file)))
  serialized_message = to_serialized_message scan_request
  out_path = File.join(out_dir, file)

  File.delete out_path if File.exists? out_path

  File.open(out_path, 'w') do |f|
    f.write(serialized_message.to_json)
  end
end

