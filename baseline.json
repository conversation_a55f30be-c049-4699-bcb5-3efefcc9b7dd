{"_links": {"root": {"href": "/"}, "self": {"href": "/scans/42b0e419-a638-4047-b21d-28384c39323d/findings"}, "help": {"href": "https://help.veracode.com/reader/tS9CaFwL4_lbIEWWomsJoA/ovfZGgu96UINQxIuTqRDwg"}}, "scan_id": "42b0e419-a638-4047-b21d-28384c39323d", "scan_status": "SUCCESS", "message": "Scan successful. Results size: 230366 bytes", "modules": ["service/target/service-1.0.76-SNAPSHOT.jar"], "modules_count": 1, "findings": [{"title": "java.lang.ProcessBuilder.start", "issue_id": 1077, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on an object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/system/SystemInfo.java", "line": 182, "function_name": "getProcessWithTemporaryWorkingDirectory", "qualified_function_name": "com.sourceclear.util.system.SystemInfo.getProcessWithTemporaryWorkingDirectory", "function_prototype": "java.lang.Process getProcessWithTemporaryWorkingDirectory(java.lang.String, SystemItem)", "scope": "com.sourceclear.util.system.SystemInfo"}}, "flaw_match": {"procedure_hash": "374257961", "prototype_hash": "1023364683", "flaw_hash": "19954854", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "3052788072", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1135241539", "cause_hash2_ordinal": "1"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1076, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the processBuilder object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/yarn/YarnLockParser.java", "line": 180, "function_name": "launchParseLockFileProcess", "qualified_function_name": "com.sourceclear.engine.component.yarn.YarnLockParser.launchParseLockFileProcess", "function_prototype": "java.lang.String launchParseLockFileProcess(java.io.File, java.io.File)", "scope": "com.sourceclear.engine.component.yarn.YarnLockParser"}}, "flaw_match": {"procedure_hash": "667818930", "prototype_hash": "3313145157", "flaw_hash": "1480817290", "flaw_hash_count": 2, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1078, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the pb object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/SbtNativeCollector.java", "line": 168, "function_name": "makeGraphBuildingProcess", "qualified_function_name": "com.sourceclear.engine.component.collectors.SbtNativeCollector.makeGraphBuildingProcess", "function_prototype": "java.lang.Process makeGraphBuildingProcess(java.io.File, java.io.File, boolean)", "scope": "com.sourceclear.engine.component.collectors.SbtNativeCollector"}}, "flaw_match": {"procedure_hash": "1415568646", "prototype_hash": "1157593049", "flaw_hash": "3152419664", "flaw_hash_count": 2, "flaw_hash_ordinal": 2, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1075, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the pb object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/SbtNativeCollector.java", "line": 309, "function_name": "makeSbtVersionQueryProcess", "qualified_function_name": "com.sourceclear.engine.component.collectors.SbtNativeCollector.makeSbtVersionQueryProcess", "function_prototype": "java.lang.Process makeSbtVersionQueryProcess(common.logging.LogStream, java.io.File)", "scope": "com.sourceclear.engine.component.collectors.SbtNativeCollector"}}, "flaw_match": {"procedure_hash": "2025124266", "prototype_hash": "1896977289", "flaw_hash": "3152419664", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1074, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the pb object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/SbtCoursierNativeCollector.java", "line": 272, "function_name": "makeGraphBuildingProcess", "qualified_function_name": "com.sourceclear.engine.component.collectors.SbtCoursierNativeCollector.makeGraphBuildingProcess", "function_prototype": "java.lang.Process makeGraphBuildingProcess(java.io.File)", "scope": "com.sourceclear.engine.component.collectors.SbtCoursierNativeCollector"}}, "flaw_match": {"procedure_hash": "4147304643", "prototype_hash": "3718298023", "flaw_hash": "3152419664", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1073, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the pb object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/SbtCollectorUtils.java", "line": 232, "function_name": "makeDependencyClasspathProcess", "qualified_function_name": "com.sourceclear.engine.component.collectors.SbtCollectorUtils.makeDependencyClasspathProcess", "function_prototype": "java.lang.Process makeDependencyClasspathProcess(common.logging.LogStream, java.io.File, java.io.File)", "scope": "com.sourceclear.engine.component.collectors.SbtCollectorUtils"}}, "flaw_match": {"procedure_hash": "752205383", "prototype_hash": "3844765253", "flaw_hash": "3152419664", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1071, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on an object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GradleNativeCollector.java", "line": 155, "function_name": "getGradleVersion", "qualified_function_name": "com.sourceclear.engine.component.collectors.GradleNativeCollector.getGradleVersion", "function_prototype": "java.lang.String getGradleVersion(java.util.List)", "scope": "com.sourceclear.engine.component.collectors.GradleNativeCollector"}}, "flaw_match": {"procedure_hash": "3323007883", "prototype_hash": "4278530097", "flaw_hash": "2662053249", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "3848896817", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2312893527", "cause_hash2_ordinal": "1"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1072, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the pb object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.lang.Process.getErrorStream, and java.lang.Process.getInputStream.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GradleNativeCollector.java", "line": 427, "function_name": "makeGraphBuildingProcess", "qualified_function_name": "com.sourceclear.engine.component.collectors.GradleNativeCollector.makeGraphBuildingProcess", "function_prototype": "java.lang.Process makeGraphBuildingProcess(java.io.File, java.io.File, boolean)", "scope": "com.sourceclear.engine.component.collectors.GradleNativeCollector"}}, "flaw_match": {"procedure_hash": "694210438", "prototype_hash": "2064368836", "flaw_hash": "3152419664", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1070, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on an object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GradleNativeCollector.java", "line": 467, "function_name": "debugScopesInAndroidProject", "qualified_function_name": "com.sourceclear.engine.component.collectors.GradleNativeCollector.debugScopesInAndroidProject", "function_prototype": "java.util.List debugScopesInAndroidProject(java.lang.String, java.io.File)", "scope": "com.sourceclear.engine.component.collectors.GradleNativeCollector"}}, "flaw_match": {"procedure_hash": "1859909823", "prototype_hash": "1643757674", "flaw_hash": "3307728689", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "3411230715", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1171894722", "cause_hash2_ordinal": "1"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1069, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the processBuilder object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GolangDepNativeCollector.java", "line": 244, "function_name": "getDotFile", "qualified_function_name": "com.sourceclear.engine.component.collectors.GolangDepNativeCollector.getDotFile", "function_prototype": "java.io.File getDotFile(java.io.File, java.util.List)", "scope": "com.sourceclear.engine.component.collectors.GolangDepNativeCollector"}}, "flaw_match": {"procedure_hash": "2208471151", "prototype_hash": "3102859093", "flaw_hash": "1480817290", "flaw_hash_count": 2, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1068, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the processBuilder object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GoPackageManagerCollector.java", "line": 474, "function_name": "getGoDependencyGraph", "qualified_function_name": "com.sourceclear.engine.component.collectors.GoPackageManagerCollector.getGoDependencyGraph", "function_prototype": "golang.GoDependencyGraph getGoDependencyGraph(java.io.File, java.io.File, java.util.List)", "scope": "com.sourceclear.engine.component.collectors.GoPackageManagerCollector"}}, "flaw_match": {"procedure_hash": "1738207319", "prototype_hash": "816052179", "flaw_hash": "1480817290", "flaw_hash_count": 3, "flaw_hash_ordinal": 2, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1067, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the process object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GoModulesNativeCollector.java", "line": 253, "function_name": "runProcess", "qualified_function_name": "com.sourceclear.engine.component.collectors.GoModulesNativeCollector.runProcess", "function_prototype": "java.lang.String runProcess(java.io.File, java.lang.ProcessBuilder)", "scope": "com.sourceclear.engine.component.collectors.GoModulesNativeCollector"}}, "flaw_match": {"procedure_hash": "889413190", "prototype_hash": "1106541610", "flaw_hash": "1480817290", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1066, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the pb object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GoModulesNativeCollector.java", "line": 342, "function_name": "makeGoVersionQueryProcess", "qualified_function_name": "com.sourceclear.engine.component.collectors.GoModulesNativeCollector.makeGoVersionQueryProcess", "function_prototype": "java.lang.Process makeGoVersionQueryProcess(java.io.File)", "scope": "com.sourceclear.engine.component.collectors.GoModulesNativeCollector"}}, "flaw_match": {"procedure_hash": "4066132027", "prototype_hash": "1446291299", "flaw_hash": "3152419664", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1065, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the processBuilder object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/CollectorUtils.java", "line": 337, "function_name": "launchProcessInternal", "qualified_function_name": "com.sourceclear.engine.component.collectors.CollectorUtils.launchProcessInternal", "function_prototype": "java.util.List launchProcessInternal(java.util.List, java.io.File, java.util.Map, veracode.security.logging.SecureLogger, java.lang.String, boolean)", "scope": "com.sourceclear.engine.component.collectors.CollectorUtils"}}, "flaw_match": {"procedure_hash": "1387455251", "prototype_hash": "3199115547", "flaw_hash": "1480817290", "flaw_hash_count": 4, "flaw_hash_ordinal": 2, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1064, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the processBuilder object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/CocoaPodsNativeCollector.java", "line": 368, "function_name": "runPodInstall", "qualified_function_name": "com.sourceclear.engine.component.collectors.CocoaPodsNativeCollector.runPodInstall", "function_prototype": "void runPodInstall(java.io.File)", "scope": "com.sourceclear.engine.component.collectors.CocoaPodsNativeCollector"}}, "flaw_match": {"procedure_hash": "4094145091", "prototype_hash": "2701527060", "flaw_hash": "1480817290", "flaw_hash_count": 3, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1063, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the processBuilder object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/Utils.java", "line": 223, "function_name": "processWithCommand", "qualified_function_name": "com.sourceclear.engine.component.Utils.processWithCommand", "function_prototype": "java.lang.String processWithCommand(java.util.List, java.nio.file.Path)", "scope": "com.sourceclear.engine.component.Utils"}}, "flaw_match": {"procedure_hash": "3896068085", "prototype_hash": "2395860167", "flaw_hash": "1480817290", "flaw_hash_count": 3, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1062, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the processBuilder object, which contains tainted data. The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/analysis/dotnet/ExecutableV2.java", "line": 234, "function_name": "execute", "qualified_function_name": "com.sourceclear.analysis.dotnet.ExecutableV2.execute", "function_prototype": "java.lang.Object execute(java.lang.String, java.nio.file.Path, utils.Utils$CheckedFunction)", "scope": "com.sourceclear.analysis.dotnet.ExecutableV2"}}, "flaw_match": {"procedure_hash": "1511603991", "prototype_hash": "1883453454", "flaw_hash": "1480817290", "flaw_hash_count": 6, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ProcessBuilder.start", "issue_id": 1061, "gob": "B", "severity": 5, "issue_type_id": "taint", "issue_type": "Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection')", "cwe_id": "78", "display_text": "<span>This call to java.lang.ProcessBuilder.start() contains a command injection flaw.  The argument to the function is constructed using untrusted input.  If an attacker is allowed to specify all or part of the command, it may be possible to execute commands on the server with the privileges of the executing process.  The level of exposure depends on the effectiveness of input validation routines, if any. start() was called on the processBuilder object, which contains tainted data. The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.  Most APIs that execute system commands also have a \"safe\" version of the method that takes an array of strings as input rather than a single string, which protects against some forms of command injection.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/78.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Command_Injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/OS-Commanding\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/analysis/dotnet/Executable.java", "line": 224, "function_name": "execute", "qualified_function_name": "com.sourceclear.analysis.dotnet.Executable.execute", "function_prototype": "java.lang.Object execute(java.lang.String, java.nio.file.Path, utils.Utils$CheckedFunction)", "scope": "com.sourceclear.analysis.dotnet.Executable"}}, "flaw_match": {"procedure_hash": "1170779376", "prototype_hash": "1072234658", "flaw_hash": "1480817290", "flaw_hash_count": 6, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.lang.ClassLoader.loadClass", "issue_id": 1021, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Use of Externally-Controlled Input to Select Classes or Code ('Unsafe Reflection')", "cwe_id": "470", "display_text": "<span>This call to java.lang.ClassLoader.loadClass() uses reflection in an unsafe manner.  An attacker can specify the class name to be instantiated, which may create unexpected control flow paths through the application.  Depending on how reflection is being used, the attack vector may allow the attacker to bypass security checks or otherwise cause the application to behave in an unexpected manner.  Even if the object does not implement the specified interface and a ClassCastException is thrown, the constructor of the untrusted class name will have already executed. The first argument to loadClass() contains tainted data from the variable className. The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate the class name against a combination of white and blocklists to ensure that only expected behavior is produced.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/470.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Unsafe_Reflection\">OWASP</a></span>", "files": {"source_file": {"file": "org/apache/commons/compress/java/util/jar/Pack200.java", "line": 69, "function_name": "run", "qualified_function_name": "org.apache.commons.compress.java.util.jar.Pack200$1.run", "function_prototype": "java.lang.Object run()", "scope": "org.apache.commons.compress.java.util.jar.Pack200$1"}}, "flaw_match": {"procedure_hash": "3806020553", "prototype_hash": "3850940339", "flaw_hash": "2140837151", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2620704574", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "6"}}, {"title": "java.lang.ClassLoader.loadClass", "issue_id": 1022, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Use of Externally-Controlled Input to Select Classes or Code ('Unsafe Reflection')", "cwe_id": "470", "display_text": "<span>This call to java.lang.ClassLoader.loadClass() uses reflection in an unsafe manner.  An attacker can specify the class name to be instantiated, which may create unexpected control flow paths through the application.  Depending on how reflection is being used, the attack vector may allow the attacker to bypass security checks or otherwise cause the application to behave in an unexpected manner.  Even if the object does not implement the specified interface and a ClassCastException is thrown, the constructor of the untrusted class name will have already executed. The first argument to loadClass() contains tainted data from the variable className. The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate the class name against a combination of white and blocklists to ensure that only expected behavior is produced.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/470.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Unsafe_Reflection\">OWASP</a></span>", "files": {"source_file": {"file": "org/apache/commons/compress/java/util/jar/Pack200.java", "line": 97, "function_name": "run", "qualified_function_name": "org.apache.commons.compress.java.util.jar.Pack200$2.run", "function_prototype": "java.lang.Object run()", "scope": "org.apache.commons.compress.java.util.jar.Pack200$2"}}, "flaw_match": {"procedure_hash": "3024562889", "prototype_hash": "3850940339", "flaw_hash": "2140837151", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2620704574", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "6"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1032, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to debug() contains tainted data from the variable escapedString. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.Process.getInputStream, and java.lang.System.getProperty.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 129, "function_name": "debug", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2766585604", "prototype_hash": "3267095663", "flaw_hash": "1486957064", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "748381785", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1035, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to debug() contains tainted data from the variable format. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, java.nio.file.Files.createTempDirectory, java.lang.ProcessBuilder.environment, java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 134, "function_name": "debug", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "47667246", "prototype_hash": "1222271396", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1036, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to debug() contains tainted data from the variable escapeMessage(). The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, java.nio.file.Files.createTempDirectory, java.lang.ProcessBuilder.environment, java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 134, "function_name": "debug", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "47667246", "prototype_hash": "1222271396", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1029, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to debug() contains tainted data from the variable str1. The tainted data originated from earlier calls to java.lang.System.getenv, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, java.nio.file.Files.createTempDirectory, and java.lang.System.getProperty.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 141, "function_name": "debug", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object, java.lang.Object)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2703576650", "prototype_hash": "3090642487", "flaw_hash": "1506295217", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "392980742", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1030, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The third argument to debug() contains tainted data from the variable str2. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.net.HttpURLConnection.getResponseMessage, java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 141, "function_name": "debug", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object, java.lang.Object)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2703576650", "prototype_hash": "3090642487", "flaw_hash": "1506295217", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "392980742", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "6"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1025, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to debug() contains tainted data from the variable format. The tainted data originated from earlier calls to java.lang.Process.getInputStream, java.lang.Process.getErrorStream, java.lang.System.getenv, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, and java.nio.file.Files.createTempDirectory.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 146, "function_name": "debug", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "3394375228", "prototype_hash": "3489248138", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1026, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to debug() contains tainted data from the variable escapeMessages(). The tainted data originated from earlier calls to java.lang.Process.getInputStream, java.lang.Process.getErrorStream, java.lang.System.getenv, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, and java.nio.file.Files.createTempDirectory.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 146, "function_name": "debug", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "3394375228", "prototype_hash": "3489248138", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.info", "issue_id": 1049, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.info() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to info() contains tainted data from the variable escapedString. The tainted data originated from earlier calls to java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 197, "function_name": "info", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.info", "function_prototype": "void info(java.lang.String)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2608898129", "prototype_hash": "3088011696", "flaw_hash": "1486957064", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "748381785", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.info", "issue_id": 1051, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.info() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to info() contains tainted data from the variable str1. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 209, "function_name": "info", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.info", "function_prototype": "void info(java.lang.String, java.lang.Object, java.lang.Object)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1273681148", "prototype_hash": "1569823765", "flaw_hash": "1506295217", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "392980742", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "org.slf4j.Logger.info", "issue_id": 1046, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.info() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to info() contains tainted data from the variable escapedString. The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 220, "function_name": "info", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.info", "function_prototype": "void info(java.lang.String, java.lang.Throwable)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1296025568", "prototype_hash": "868209269", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.info", "issue_id": 1047, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.info() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to info() contains tainted data from the variables (new SecureExceptionWrapper(...)). The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 220, "function_name": "info", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.info", "function_prototype": "void info(java.lang.String, java.lang.Throwable)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1296025568", "prototype_hash": "868209269", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.warn", "issue_id": 1054, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.warn() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to warn() contains tainted data from the variable format. The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 275, "function_name": "warn", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.warn", "function_prototype": "void warn(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2427377408", "prototype_hash": "4126424613", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.warn", "issue_id": 1055, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.warn() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to warn() contains tainted data from the variable escapeMessages(). The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 275, "function_name": "warn", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.warn", "function_prototype": "void warn(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2427377408", "prototype_hash": "4126424613", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.error", "issue_id": 1043, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.error() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to error() contains tainted data from the variable escapedString. The tainted data originated from an earlier call to java.net.http.HttpClient.send.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 333, "function_name": "error", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.error", "function_prototype": "void error(java.lang.String)", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "108386209", "prototype_hash": "2699345323", "flaw_hash": "1486957064", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "748381785", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.error", "issue_id": 1039, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.error() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to error() contains tainted data from the variable format. The tainted data originated from an earlier call to java.net.HttpURLConnection.getResponseMessage.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 350, "function_name": "error", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.error", "function_prototype": "void error(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1482096539", "prototype_hash": "2671438279", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.error", "issue_id": 1040, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.error() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to error() contains tainted data from the variable escapeMessages(). The tainted data originated from an earlier call to java.net.HttpURLConnection.getResponseMessage.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/shaded/security/logging/SecureLogger.java", "line": 350, "function_name": "error", "qualified_function_name": "com.veracode.shaded.security.logging.SecureLogger.error", "function_prototype": "void error(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.shaded.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1482096539", "prototype_hash": "2671438279", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1031, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to debug() contains tainted data from the variable escapedString. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.Process.getInputStream, and java.lang.System.getProperty.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 129, "function_name": "debug", "qualified_function_name": "com.veracode.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "3872417880", "prototype_hash": "3784629054", "flaw_hash": "1486957064", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "748381785", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1034, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to debug() contains tainted data from the variable escapeMessage(). The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, java.nio.file.Files.createTempDirectory, java.lang.ProcessBuilder.environment, java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 134, "function_name": "debug", "qualified_function_name": "com.veracode.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2550076221", "prototype_hash": "3975015638", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1033, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to debug() contains tainted data from the variable format. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, java.nio.file.Files.createTempDirectory, java.lang.ProcessBuilder.environment, java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 134, "function_name": "debug", "qualified_function_name": "com.veracode.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2550076221", "prototype_hash": "3975015638", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1027, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to debug() contains tainted data from the variable str1. The tainted data originated from earlier calls to java.lang.System.getenv, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, java.nio.file.Files.createTempDirectory, and java.lang.System.getProperty.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 141, "function_name": "debug", "qualified_function_name": "com.veracode.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object, java.lang.Object)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1865302645", "prototype_hash": "3528096369", "flaw_hash": "1506295217", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "392980742", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1028, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The third argument to debug() contains tainted data from the variable str2. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.net.HttpURLConnection.getResponseMessage, java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 141, "function_name": "debug", "qualified_function_name": "com.veracode.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object, java.lang.Object)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1865302645", "prototype_hash": "3528096369", "flaw_hash": "1506295217", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "392980742", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "6"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1023, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to debug() contains tainted data from the variable format. The tainted data originated from earlier calls to java.lang.Process.getInputStream, java.lang.Process.getErrorStream, java.lang.System.getenv, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, and java.nio.file.Files.createTempDirectory.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 146, "function_name": "debug", "qualified_function_name": "com.veracode.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1742097206", "prototype_hash": "2163198415", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.debug", "issue_id": 1024, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.debug() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to debug() contains tainted data from the variable escapeMessages(). The tainted data originated from earlier calls to java.lang.Process.getInputStream, java.lang.Process.getErrorStream, java.lang.System.getenv, java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, and java.nio.file.Files.createTempDirectory.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 146, "function_name": "debug", "qualified_function_name": "com.veracode.security.logging.SecureLogger.debug", "function_prototype": "void debug(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1742097206", "prototype_hash": "2163198415", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.info", "issue_id": 1048, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.info() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to info() contains tainted data from the variable escapedString. The tainted data originated from earlier calls to java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 197, "function_name": "info", "qualified_function_name": "com.veracode.security.logging.SecureLogger.info", "function_prototype": "void info(java.lang.String)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "218022047", "prototype_hash": "3467700329", "flaw_hash": "1486957064", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "748381785", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.info", "issue_id": 1050, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.info() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to info() contains tainted data from the variable str1. The tainted data originated from earlier calls to java.lang.System.getenv, java.lang.System.getProperty, java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 209, "function_name": "info", "qualified_function_name": "com.veracode.security.logging.SecureLogger.info", "function_prototype": "void info(java.lang.String, java.lang.Object, java.lang.Object)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "315288427", "prototype_hash": "475032347", "flaw_hash": "1506295217", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "392980742", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "org.slf4j.Logger.info", "issue_id": 1044, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.info() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to info() contains tainted data from the variable escapedString. The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 220, "function_name": "info", "qualified_function_name": "com.veracode.security.logging.SecureLogger.info", "function_prototype": "void info(java.lang.String, java.lang.Throwable)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2033142536", "prototype_hash": "682956326", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.info", "issue_id": 1045, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.info() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to info() contains tainted data from the variables (new SecureExceptionWrapper(...)). The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 220, "function_name": "info", "qualified_function_name": "com.veracode.security.logging.SecureLogger.info", "function_prototype": "void info(java.lang.String, java.lang.Throwable)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2033142536", "prototype_hash": "682956326", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.warn", "issue_id": 1052, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.warn() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to warn() contains tainted data from the variable format. The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 275, "function_name": "warn", "qualified_function_name": "com.veracode.security.logging.SecureLogger.warn", "function_prototype": "void warn(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "662164805", "prototype_hash": "3889211888", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.warn", "issue_id": 1053, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.warn() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to warn() contains tainted data from the variable escapeMessages(). The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 275, "function_name": "warn", "qualified_function_name": "com.veracode.security.logging.SecureLogger.warn", "function_prototype": "void warn(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "662164805", "prototype_hash": "3889211888", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.slf4j.Logger.error", "issue_id": 1042, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.error() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to error() contains tainted data from the variable escapedString. The tainted data originated from an earlier call to java.net.http.HttpClient.send.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 333, "function_name": "error", "qualified_function_name": "com.veracode.security.logging.SecureLogger.error", "function_prototype": "void error(java.lang.String)", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "2852339603", "prototype_hash": "1229561840", "flaw_hash": "1486957064", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "748381785", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.error", "issue_id": 1037, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.error() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to error() contains tainted data from the variable format. The tainted data originated from an earlier call to java.net.HttpURLConnection.getResponseMessage.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 350, "function_name": "error", "qualified_function_name": "com.veracode.security.logging.SecureLogger.error", "function_prototype": "void error(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1894349911", "prototype_hash": "1007602152", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "org.slf4j.Logger.error", "issue_id": 1038, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.error() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The second argument to error() contains tainted data from the variable escapeMessages(). The tainted data originated from an earlier call to java.net.HttpURLConnection.getResponseMessage.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/veracode/security/logging/SecureLogger.java", "line": 350, "function_name": "error", "qualified_function_name": "com.veracode.security.logging.SecureLogger.error", "function_prototype": "void error(java.lang.String, java.lang.Object  [])", "scope": "com.veracode.security.logging.SecureLogger"}}, "flaw_match": {"procedure_hash": "1894349911", "prototype_hash": "1007602152", "flaw_hash": "693509759", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1747518313", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "org.yaml.snakeyaml.Yaml.load", "issue_id": 1060, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Deserialization of Untrusted Data", "cwe_id": "502", "display_text": "<span>The serialized-object data stream used in the call to org.yaml.snakeyaml.Yaml.load() appears to have been constructed with untrusted data. Attacker manipulation of this stream has the ability to cause the creation of objects of arbitrary Serializable types.  Paired with a weakness in another class's constructor, this could result in a denial of service, code execution, or data corruption vulnerability. The first argument to load() contains tainted data from the variable is. The tainted data originated from an earlier call to java.io.File.!ctor.</span> <span>Avoid passing untrusted data; if the data is untrusted, consider switching to a safer serialization scheme such as JSON.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/502.html\">CWE</a></span>", "files": {"source_file": {"file": "com/srcclr/sdk/Directives.java", "line": 35, "function_name": "parseDirectives", "qualified_function_name": "com.srcclr.sdk.Directives.parseDirectives", "function_prototype": "java.util.Map parseDirectives(java.io.InputStream)", "scope": "com.srcclr.sdk.Directives"}}, "flaw_match": {"procedure_hash": "1993755640", "prototype_hash": "2193897400", "flaw_hash": "729100661", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "3650430736", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "6"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1104, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable first. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/security/SecurityUtils.java", "line": 35, "function_name": "securePathsGet", "qualified_function_name": "com.sourceclear.util.security.SecurityUtils.securePathsGet", "function_prototype": "java.nio.file.Path securePathsGet(java.lang.String, java.lang.String  [])", "scope": "com.sourceclear.util.security.SecurityUtils"}}, "flaw_match": {"procedure_hash": "2742942941", "prototype_hash": "618659812", "flaw_hash": "3392777041", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1176028798", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1105, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variable more. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/security/SecurityUtils.java", "line": 35, "function_name": "securePathsGet", "qualified_function_name": "com.sourceclear.util.security.SecurityUtils.securePathsGet", "function_prototype": "java.nio.file.Path securePathsGet(java.lang.String, java.lang.String  [])", "scope": "com.sourceclear.util.security.SecurityUtils"}}, "flaw_match": {"procedure_hash": "2742942941", "prototype_hash": "618659812", "flaw_hash": "3392777041", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1176028798", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "java.nio.file.Path.resolve", "issue_id": 1080, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Path.resolve() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to resolve() contains tainted data from the variable suppliedPath. The tainted data originated from earlier calls to java.nio.file.Path.resolve, java.nio.file.Paths.get, java.io.File.!ctor, java.nio.file.Files.createTempDirectory, and java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/security/SecurityUtils.java", "line": 58, "function_name": "securePathsResolve", "qualified_function_name": "com.sourceclear.util.security.SecurityUtils.securePathsResolve", "function_prototype": "java.nio.file.Path securePathsResolve(java.nio.file.Path, java.lang.String  [])", "scope": "com.sourceclear.util.security.SecurityUtils"}}, "flaw_match": {"procedure_hash": "1311059480", "prototype_hash": "3593603368", "flaw_hash": "3392777041", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1176028798", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1019, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to !operator_javanewinit() contains tainted data from the variable filename. The tainted data originated from earlier calls to java.lang.System.getProperty, and java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/io/IO.java", "line": 59, "function_name": "downloadFileIntoDir", "qualified_function_name": "com.sourceclear.util.io.IO.downloadFileIntoDir", "function_prototype": "java.io.File downloadFileIntoDir(java.net.URL, java.lang.String, java.io.File, java.util.EnumSet, boolean, int)", "scope": "com.sourceclear.util.io.IO"}}, "flaw_match": {"procedure_hash": "1070932403", "prototype_hash": "2932549147", "flaw_hash": "3392777041", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1176028798", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "org.apache.commons.codec.digest.DigestUtils.md5Hex", "issue_id": 1059, "gob": "B", "severity": 3, "issue_type_id": "crypto", "issue_type": "Use of a Broken or Risky Cryptographic Algorithm", "cwe_id": "327", "display_text": "<span>This function uses the org.apache.commons.codec.digest.DigestUtils.md5Hex() function, which uses a hash algorithm that is considered weak. In recent years, researchers have demonstrated ways to breach many uses of previously-thought-safe hash functions such as MD5. </span> <span>Consider using a stronger algorithm in order to prevent attackers from being able to manipulate hash results. If this algorithm is being used to hash passwords, then consider using a strong computationally-hard algorithm such as PBKDF2 or bcrypt instead of a plain hashing algorithm.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/327.html\">CWE</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/io/IO.java", "line": 127, "function_name": "downloadFileIntoDir", "qualified_function_name": "com.sourceclear.util.io.IO.downloadFileIntoDir", "function_prototype": "java.io.File downloadFileIntoDir(java.net.URL, java.lang.String, java.io.File, java.util.EnumSet, boolean, int)", "scope": "com.sourceclear.util.io.IO"}}, "flaw_match": {"procedure_hash": "1070932403", "prototype_hash": "2932549147", "flaw_hash": "1480817290", "flaw_hash_count": 5, "flaw_hash_ordinal": 5, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1102, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable path. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/io/ExecutableResolver.java", "line": 84, "function_name": "resolve", "qualified_function_name": "com.sourceclear.util.io.ExecutableResolver.resolve", "function_prototype": "java.lang.String resolve(java.lang.String)", "scope": "com.sourceclear.util.io.ExecutableResolver"}}, "flaw_match": {"procedure_hash": "2742226733", "prototype_hash": "3733454370", "flaw_hash": "2200719846", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1379018062", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1103, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variables (new String\\[...\\]). The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/io/ExecutableResolver.java", "line": 84, "function_name": "resolve", "qualified_function_name": "com.sourceclear.util.io.ExecutableResolver.resolve", "function_prototype": "java.lang.String resolve(java.lang.String)", "scope": "com.sourceclear.util.io.ExecutableResolver"}}, "flaw_match": {"procedure_hash": "2742226733", "prototype_hash": "3733454370", "flaw_hash": "2200719846", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1379018062", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "4287583976", "cause_hash2_ordinal": "1"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1017, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable graphFilename. The tainted data originated from earlier calls to java.lang.Process.getInputStream, java.lang.Process.getErrorStream, and java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/util/io/EvidenceUtils.java", "line": 152, "function_name": "evidenceFromLibraryGraphs", "qualified_function_name": "com.sourceclear.util.io.EvidenceUtils.evidenceFromLibraryGraphs", "function_prototype": "java.util.Collection evidenceFromLibraryGraphs(java.util.Collection, boolean)", "scope": "com.sourceclear.util.io.EvidenceUtils"}}, "flaw_match": {"procedure_hash": "2061133370", "prototype_hash": "2499841256", "flaw_hash": "2724161910", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "6"}}, {"title": "java.security.MessageDigest.getInstance", "issue_id": 1057, "gob": "B", "severity": 3, "issue_type_id": "crypto", "issue_type": "Use of a Broken or Risky Cryptographic Algorithm", "cwe_id": "327", "display_text": "<span>This function uses the SHA-1() function, which uses a hash algorithm that is considered weak. In recent years, researchers have demonstrated ways to breach many uses of previously-thought-safe hash functions such as MD5. </span> <span>Consider using a stronger algorithm in order to prevent attackers from being able to manipulate hash results. If this algorithm is being used to hash passwords, then consider using a strong computationally-hard algorithm such as PBKDF2 or bcrypt instead of a plain hashing algorithm.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/327.html\">CWE</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/jarutils/BasicJarAnalyzer.java", "line": 51, "function_name": "analyze", "qualified_function_name": "com.sourceclear.engine.component.jarutils.BasicJarAnalyzer.analyze", "function_prototype": "java.util.Set analyze(bytecode.JarInputStreamGenerator, java.lang.String)", "scope": "com.sourceclear.engine.component.jarutils.BasicJarAnalyzer"}}, "flaw_match": {"procedure_hash": "4018915840", "prototype_hash": "278026457", "flaw_hash": "2659753404", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "3520434851", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1099, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variables (new String\\[...\\]). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/golang/GoPackageManagerCommandExecutor.java", "line": 102, "function_name": "createGoPathAndProject", "qualified_function_name": "com.sourceclear.engine.component.golang.GoPackageManagerCommandExecutor.createGoPathAndProject", "function_prototype": "org.apache.commons.lang3.tuple.Pair createGoPathAndProject(java.io.File, java.lang.String)", "scope": "com.sourceclear.engine.component.golang.GoPackageManagerCommandExecutor"}}, "flaw_match": {"procedure_hash": "3675704541", "prototype_hash": "2496404291", "flaw_hash": "3492561778", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2043017950", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1021168670", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1098, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable tempDirPath. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/golang/GoPackageManagerCommandExecutor.java", "line": 102, "function_name": "createGoPathAndProject", "qualified_function_name": "com.sourceclear.engine.component.golang.GoPackageManagerCommandExecutor.createGoPathAndProject", "function_prototype": "org.apache.commons.lang3.tuple.Pair createGoPathAndProject(java.io.File, java.lang.String)", "scope": "com.sourceclear.engine.component.golang.GoPackageManagerCommandExecutor"}}, "flaw_match": {"procedure_hash": "3675704541", "prototype_hash": "2496404291", "flaw_hash": "3492561778", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2043017950", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1100, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable userGoPathString. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/golang/GoPackageManagerCommandExecutor.java", "line": 127, "function_name": "getProjectImportPath", "qualified_function_name": "com.sourceclear.engine.component.golang.GoPackageManagerCommandExecutor.getProjectImportPath", "function_prototype": "java.lang.String getProjectImportPath(java.util.List, java.io.File, java.lang.String)", "scope": "com.sourceclear.engine.component.golang.GoPackageManagerCommandExecutor"}}, "flaw_match": {"procedure_hash": "1003656575", "prototype_hash": "1193101235", "flaw_hash": "2304891605", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "3255726746", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1101, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variables (new String\\[...\\]). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/golang/GoPackageManagerCommandExecutor.java", "line": 127, "function_name": "getProjectImportPath", "qualified_function_name": "com.sourceclear.engine.component.golang.GoPackageManagerCommandExecutor.getProjectImportPath", "function_prototype": "java.lang.String getProjectImportPath(java.util.List, java.io.File, java.lang.String)", "scope": "com.sourceclear.engine.component.golang.GoPackageManagerCommandExecutor"}}, "flaw_match": {"procedure_hash": "1003656575", "prototype_hash": "1193101235", "flaw_hash": "2304891605", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "3255726746", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1560428298", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1096, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable getProperty(). The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/git/GitCloner.java", "line": 122, "function_name": "getDefaultCloneRoot", "qualified_function_name": "com.sourceclear.engine.component.git.GitCloner.getDefaultCloneRoot", "function_prototype": "java.nio.file.Path getDefaultCloneRoot(void)", "scope": "com.sourceclear.engine.component.git.GitCloner"}}, "flaw_match": {"procedure_hash": "3148126957", "prototype_hash": "4146034803", "flaw_hash": "2481159399", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1973573538", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "301912510", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1097, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variables (new String\\[...\\]). The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/git/GitCloner.java", "line": 122, "function_name": "getDefaultCloneRoot", "qualified_function_name": "com.sourceclear.engine.component.git.GitCloner.getDefaultCloneRoot", "function_prototype": "java.nio.file.Path getDefaultCloneRoot(void)", "scope": "com.sourceclear.engine.component.git.GitCloner"}}, "flaw_match": {"procedure_hash": "3148126957", "prototype_hash": "4146034803", "flaw_hash": "2481159399", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1973573538", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2758578959", "cause_hash2_ordinal": "1"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1016, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable jarPath. The tainted data originated from earlier calls to java.lang.Process.getInputStream, and java.lang.Process.getErrorStream.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/SbtCollectorUtils.java", "line": 216, "function_name": "libraryGraphFromJarList", "qualified_function_name": "com.sourceclear.engine.component.collectors.SbtCollectorUtils.libraryGraphFromJarList", "function_prototype": "java.util.Set libraryGraphFromJarList(java.util.List)", "scope": "com.sourceclear.engine.component.collectors.SbtCollectorUtils"}}, "flaw_match": {"procedure_hash": "3595065207", "prototype_hash": "4021601652", "flaw_hash": "3094720564", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "6"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1014, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable userPythonPath. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/PIPNativeCollector.java", "line": 106, "function_name": "ensureSystemRequirements", "qualified_function_name": "com.sourceclear.engine.component.collectors.PIPNativeCollector.ensureSystemRequirements", "function_prototype": "void ensureSystemRequirements()", "scope": "com.sourceclear.engine.component.collectors.PIPNativeCollector"}}, "flaw_match": {"procedure_hash": "4087492072", "prototype_hash": "4146034803", "flaw_hash": "1480817290", "flaw_hash_count": 2, "flaw_hash_ordinal": 2, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1015, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable str. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/PIPNativeCollector.java", "line": 434, "function_name": "getRequirementsFileFromAttributes", "qualified_function_name": "com.sourceclear.engine.component.collectors.PIPNativeCollector.getRequirementsFileFromAttributes", "function_prototype": "java.io.File getRequirementsFileFromAttributes(java.util.Map)", "scope": "com.sourceclear.engine.component.collectors.PIPNativeCollector"}}, "flaw_match": {"procedure_hash": "1674428782", "prototype_hash": "1732744662", "flaw_hash": "3650229804", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 2, "cause_hash_ordinal": 2, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1092, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable getProperty(). The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/MavenNativeCollector.java", "line": 71, "function_name": "<clinit>", "qualified_function_name": "com.sourceclear.engine.component.collectors.MavenNativeCollector.<clinit>", "function_prototype": "void <clinit>(void)", "scope": "com.sourceclear.engine.component.collectors.MavenNativeCollector"}}, "flaw_match": {"procedure_hash": "2559968694", "prototype_hash": "211846691", "flaw_hash": "352985575", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4086626120", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "301912510", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1093, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variables (new String\\[...\\]). The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/MavenNativeCollector.java", "line": 71, "function_name": "<clinit>", "qualified_function_name": "com.sourceclear.engine.component.collectors.MavenNativeCollector.<clinit>", "function_prototype": "void <clinit>(void)", "scope": "com.sourceclear.engine.component.collectors.MavenNativeCollector"}}, "flaw_match": {"procedure_hash": "2559968694", "prototype_hash": "211846691", "flaw_hash": "352985575", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4086626120", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "305106918", "cause_hash2_ordinal": "1"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1013, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable mavenPath. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/MavenNativeCollector.java", "line": 263, "function_name": "resolveMvnOrThrow", "qualified_function_name": "com.sourceclear.engine.component.collectors.MavenNativeCollector.resolveMvnOrThrow", "function_prototype": "java.io.File resolveMvnOrThrow(java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.MavenNativeCollector"}}, "flaw_match": {"procedure_hash": "1422073114", "prototype_hash": "3501262674", "flaw_hash": "2687389710", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1094, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable x$0. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/MavenNativeCollector.java", "line": 327, "function_name": "lambda$getOrCreateMvnWrapperFile$0", "qualified_function_name": "com.sourceclear.engine.component.collectors.MavenNativeCollector.lambda$getOrCreateMvnWrapperFile$0", "function_prototype": "java.nio.file.Path lambda$getOrCreateMvnWrapperFile$0(java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.MavenNativeCollector"}}, "flaw_match": {"procedure_hash": "3804646805", "prototype_hash": "509811913", "flaw_hash": "2027733816", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4143052747", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1095, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable x$0. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/MavenNativeCollector.java", "line": 340, "function_name": "lambda$getOrCreateMvnWrapperFile$1", "qualified_function_name": "com.sourceclear.engine.component.collectors.MavenNativeCollector.lambda$getOrCreateMvnWrapperFile$1", "function_prototype": "java.nio.file.Path lambda$getOrCreateMvnWrapperFile$1(java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.MavenNativeCollector"}}, "flaw_match": {"procedure_hash": "2417285246", "prototype_hash": "2737733022", "flaw_hash": "2027733816", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4143052747", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1091, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable value. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/MakefileNativeCollector.java", "line": 410, "function_name": "getLDLibraryPaths", "qualified_function_name": "com.sourceclear.engine.component.collectors.MakefileNativeCollector.getLDLibraryPaths", "function_prototype": "java.util.Set getLDLibraryPaths(void)", "scope": "com.sourceclear.engine.component.collectors.MakefileNativeCollector"}}, "flaw_match": {"procedure_hash": "3517455425", "prototype_hash": "4146034803", "flaw_hash": "4280415042", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4143052747", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1012, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable execPath. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/MSBuildDotNetNativeCollector.java", "line": 192, "function_name": "ensureCustomExec", "qualified_function_name": "com.sourceclear.engine.component.collectors.MSBuildDotNetNativeCollector.ensureCustomExec", "function_prototype": "boolean ensureCustomExec(java.lang.String, MSBuildDotNetNativeCollector$DotNetExec)", "scope": "com.sourceclear.engine.component.collectors.MSBuildDotNetNativeCollector"}}, "flaw_match": {"procedure_hash": "2969465127", "prototype_hash": "3537303578", "flaw_hash": "1184068760", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "3"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1011, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable getProperty(). The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/MSBuildDotNetNativeCollector.java", "line": 389, "function_name": "checkDotNetExecInfo", "qualified_function_name": "com.sourceclear.engine.component.collectors.MSBuildDotNetNativeCollector.checkDotNetExecInfo", "function_prototype": "void checkDotNetExecInfo()", "scope": "com.sourceclear.engine.component.collectors.MSBuildDotNetNativeCollector"}}, "flaw_match": {"procedure_hash": "303432835", "prototype_hash": "4146034803", "flaw_hash": "3896162269", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "883984638", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "153777825", "cause_hash2_ordinal": "1"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1010, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable gradlePath. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GradleNativeCollector.java", "line": 320, "function_name": "resolveGradleExeOrThrow", "qualified_function_name": "com.sourceclear.engine.component.collectors.GradleNativeCollector.resolveGradleExeOrThrow", "function_prototype": "java.io.File resolveGradleExeOrThrow()", "scope": "com.sourceclear.engine.component.collectors.GradleNativeCollector"}}, "flaw_match": {"procedure_hash": "2935384295", "prototype_hash": "3850940339", "flaw_hash": "3885022286", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "3"}}, {"title": "java.nio.file.Path.resolve", "issue_id": 1079, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Path.resolve() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to resolve() contains tainted data from the variable gradleLocation. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GradleNativeCollector.java", "line": 512, "function_name": "<PERSON><PERSON><PERSON><PERSON>", "qualified_function_name": "com.sourceclear.engine.component.collectors.GradleNativeCollector.getGradlew", "function_prototype": "java.util.Optional getGradlew(java.nio.file.Path, java.util.Map)", "scope": "com.sourceclear.engine.component.collectors.GradleNativeCollector"}}, "flaw_match": {"procedure_hash": "223640428", "prototype_hash": "2967697855", "flaw_hash": "663710619", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1176028798", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1090, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable x$0. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GradleNativeCollector.java", "line": 514, "function_name": "lambda$getGradlew$2", "qualified_function_name": "com.sourceclear.engine.component.collectors.GradleNativeCollector.lambda$getGradlew$2", "function_prototype": "java.nio.file.Path lambda$getGradlew$2(java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.GradleNativeCollector"}}, "flaw_match": {"procedure_hash": "3895103484", "prototype_hash": "3734402683", "flaw_hash": "2027733816", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4143052747", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "2"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1089, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variables (new String\\[...\\]). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GolangDepNativeCollector.java", "line": 185, "function_name": "getResolvedImports", "qualified_function_name": "com.sourceclear.engine.component.collectors.GolangDepNativeCollector.getResolvedImports", "function_prototype": "java.util.List getResolvedImports(java.io.File, golang.GoDependencyGraph, java.util.List)", "scope": "com.sourceclear.engine.component.collectors.GolangDepNativeCollector"}}, "flaw_match": {"procedure_hash": "3284863220", "prototype_hash": "2650258702", "flaw_hash": "1483707097", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "856815187", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "189661549", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1088, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable projectRoot.getAbsolutePath(). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GolangDepNativeCollector.java", "line": 185, "function_name": "getResolvedImports", "qualified_function_name": "com.sourceclear.engine.component.collectors.GolangDepNativeCollector.getResolvedImports", "function_prototype": "java.util.List getResolvedImports(java.io.File, golang.GoDependencyGraph, java.util.List)", "scope": "com.sourceclear.engine.component.collectors.GolangDepNativeCollector"}}, "flaw_match": {"procedure_hash": "3284863220", "prototype_hash": "2650258702", "flaw_hash": "1483707097", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "856815187", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1086, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable projectRoot.getAbsolutePath(). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GlideNativeCollector.java", "line": 93, "function_name": "getResolvedImports", "qualified_function_name": "com.sourceclear.engine.component.collectors.GlideNativeCollector.getResolvedImports", "function_prototype": "java.util.List getResolvedImports(java.io.File, golang.GoDependencyGraph, java.util.List)", "scope": "com.sourceclear.engine.component.collectors.GlideNativeCollector"}}, "flaw_match": {"procedure_hash": "3810506809", "prototype_hash": "657195199", "flaw_hash": "1387774397", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "20442561", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1087, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variables (new String\\[...\\]). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GlideNativeCollector.java", "line": 93, "function_name": "getResolvedImports", "qualified_function_name": "com.sourceclear.engine.component.collectors.GlideNativeCollector.getResolvedImports", "function_prototype": "java.util.List getResolvedImports(java.io.File, golang.GoDependencyGraph, java.util.List)", "scope": "com.sourceclear.engine.component.collectors.GlideNativeCollector"}}, "flaw_match": {"procedure_hash": "3810506809", "prototype_hash": "657195199", "flaw_hash": "1387774397", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "20442561", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "493313985", "cause_hash2_ordinal": "1"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1009, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable bundlePath. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GemNativeCollector.java", "line": 314, "function_name": "resolveBundleExeOrThrow", "qualified_function_name": "com.sourceclear.engine.component.collectors.GemNativeCollector.resolveBundleExeOrThrow", "function_prototype": "java.io.File resolveBundleExeOrThrow()", "scope": "com.sourceclear.engine.component.collectors.GemNativeCollector"}}, "flaw_match": {"procedure_hash": "3353807498", "prototype_hash": "3850940339", "flaw_hash": "1480817290", "flaw_hash_count": 2, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "4"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1008, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable filePath. The tainted data originated from earlier calls to java.lang.System.getenv, and java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/CollectorUtils.java", "line": 222, "function_name": "resolveExeOrThrow", "qualified_function_name": "com.sourceclear.engine.component.collectors.CollectorUtils.resolveExeOrThrow", "function_prototype": "java.io.File resolveExeOrThrow(java.lang.String, java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.CollectorUtils"}}, "flaw_match": {"procedure_hash": "3083817751", "prototype_hash": "1311749325", "flaw_hash": "4004373185", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1106, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable token.trim(). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/AntNativeCollector.java", "line": 407, "function_name": "launchAntProcessAndGetLibraryGraphContainer", "qualified_function_name": "com.sourceclear.engine.component.collectors.AntNativeCollector.launchAntProcessAndGetLibraryGraphContainer", "function_prototype": "srcclr.sdk.LibraryGraphContainer launchAntProcessAndGetLibraryGraphContainer(java.io.File, java.io.File, java.io.File, java.lang.String, java.lang.Boolean, java.lang.String, java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.AntNativeCollector"}}, "flaw_match": {"procedure_hash": "2455414020", "prototype_hash": "2926367624", "flaw_hash": "3055859404", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4129839859", "cause_hash_count": 2, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1107, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable token.trim(). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/AntNativeCollector.java", "line": 408, "function_name": "launchAntProcessAndGetLibraryGraphContainer", "qualified_function_name": "com.sourceclear.engine.component.collectors.AntNativeCollector.launchAntProcessAndGetLibraryGraphContainer", "function_prototype": "srcclr.sdk.LibraryGraphContainer launchAntProcessAndGetLibraryGraphContainer(java.io.File, java.io.File, java.io.File, java.lang.String, java.lang.Boolean, java.lang.String, java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.AntNativeCollector"}}, "flaw_match": {"procedure_hash": "2455414020", "prototype_hash": "2926367624", "flaw_hash": "2118150349", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4129839859", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1084, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable projectRoot.getAbsolutePath(). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/AntNativeCollector.java", "line": 410, "function_name": "launchAntProcessAndGetLibraryGraphContainer", "qualified_function_name": "com.sourceclear.engine.component.collectors.AntNativeCollector.launchAntProcessAndGetLibraryGraphContainer", "function_prototype": "srcclr.sdk.LibraryGraphContainer launchAntProcessAndGetLibraryGraphContainer(java.io.File, java.io.File, java.io.File, java.lang.String, java.lang.Boolean, java.lang.String, java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.AntNativeCollector"}}, "flaw_match": {"procedure_hash": "2455414020", "prototype_hash": "2926367624", "flaw_hash": "2598391748", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "496399354", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1085, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The second argument to get() contains tainted data from the variables (new String\\[...\\]). The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/AntNativeCollector.java", "line": 410, "function_name": "launchAntProcessAndGetLibraryGraphContainer", "qualified_function_name": "com.sourceclear.engine.component.collectors.AntNativeCollector.launchAntProcessAndGetLibraryGraphContainer", "function_prototype": "srcclr.sdk.LibraryGraphContainer launchAntProcessAndGetLibraryGraphContainer(java.io.File, java.io.File, java.io.File, java.lang.String, java.lang.Boolean, java.lang.String, java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.AntNativeCollector"}}, "flaw_match": {"procedure_hash": "2455414020", "prototype_hash": "2926367624", "flaw_hash": "2598391748", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "496399354", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "3264848928", "cause_hash2_ordinal": "1"}}, {"title": "java.io.File.!operator_javanew<PERSON>t", "issue_id": 1007, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.io.File.!operator_javanewinit() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to !operator_javanewinit() contains tainted data from the variable antPath. The tainted data originated from an earlier call to java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/AntNativeCollector.java", "line": 500, "function_name": "resolveAntExeOrThrow", "qualified_function_name": "com.sourceclear.engine.component.collectors.AntNativeCollector.resolveAntExeOrThrow", "function_prototype": "java.io.File resolveAntExeOrThrow()", "scope": "com.sourceclear.engine.component.collectors.AntNativeCollector"}}, "flaw_match": {"procedure_hash": "81150375", "prototype_hash": "3850940339", "flaw_hash": "2687389710", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1522093433", "cause_hash2_ordinal": "5"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1083, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable graph.getFilename(). The tainted data originated from earlier calls to java.lang.Process.getInputStream, java.lang.Process.getErrorStream, and java.lang.System.getenv.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/RecursiveEngine.java", "line": 371, "function_name": "rewriteGraph", "qualified_function_name": "com.sourceclear.engine.component.RecursiveEngine.rewriteGraph", "function_prototype": "srcclr.sdk.LibraryGraph rewriteGraph(java.nio.file.Path, java.nio.file.Path, srcclr.sdk.LibraryGraph)", "scope": "com.sourceclear.engine.component.RecursiveEngine"}}, "flaw_match": {"procedure_hash": "2111628384", "prototype_hash": "1347536502", "flaw_hash": "1169432452", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4129839859", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "2866949028", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1081, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable getProperty(). The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/common/ProcessUtils.java", "line": 97, "function_name": "launchProcess", "qualified_function_name": "com.sourceclear.engine.common.ProcessUtils.launchProcess", "function_prototype": "java.lang.String launchProcess(java.util.List)", "scope": "com.sourceclear.engine.common.ProcessUtils"}}, "flaw_match": {"procedure_hash": "3785516400", "prototype_hash": "1150486115", "flaw_hash": "2639944", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2160088357", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "153777825", "cause_hash2_ordinal": "1"}}, {"title": "java.nio.file.Paths.get", "issue_id": 1082, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "External Control of File Name or Path", "cwe_id": "73", "display_text": "<span>This call to java.nio.file.Paths.get() contains a path manipulation flaw.  The argument to the function is a filename constructed using untrusted input.  If an attacker is allowed to specify all or part of the filename, it may be possible to gain unauthorized access to files on the server, including those outside the webroot, that would be normally be inaccessible to end users.  The level of exposure depends on the effectiveness of input validation routines, if any. The first argument to get() contains tainted data from the variable getProperty(). The tainted data originated from an earlier call to java.lang.System.getProperty.</span> <span>Validate all untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.  When using blocklists, be sure that the sanitizing routine performs a sufficient number of iterations to remove all instances of disallowed characters.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/73.html\">CWE</a> <a href=\"https://webappsec.pbworks.com/Path-Traversal\">WASC</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/common/ProcessUtils.java", "line": 105, "function_name": "launchProcess", "qualified_function_name": "com.sourceclear.engine.common.ProcessUtils.launchProcess", "function_prototype": "int launchProcess(java.util.List, java.util.function.Consumer)", "scope": "com.sourceclear.engine.common.ProcessUtils"}}, "flaw_match": {"procedure_hash": "1871185887", "prototype_hash": "3452742144", "flaw_hash": "2639944", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2160088357", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "153777825", "cause_hash2_ordinal": "1"}}, {"title": "java.security.MessageDigest.getInstance", "issue_id": 1056, "gob": "B", "severity": 3, "issue_type_id": "crypto", "issue_type": "Use of a Broken or Risky Cryptographic Algorithm", "cwe_id": "327", "display_text": "<span>This function uses the SHA-1() function, which uses a hash algorithm that is considered weak. In recent years, researchers have demonstrated ways to breach many uses of previously-thought-safe hash functions such as MD5. </span> <span>Consider using a stronger algorithm in order to prevent attackers from being able to manipulate hash results. If this algorithm is being used to hash passwords, then consider using a strong computationally-hard algorithm such as PBKDF2 or bcrypt instead of a plain hashing algorithm.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/327.html\">CWE</a></span>", "files": {"source_file": {"file": "com/sourceclear/bytecode/v2/ClassFingerprint.java", "line": 59, "function_name": "computeHashInternally", "qualified_function_name": "com.sourceclear.bytecode.v2.ClassFingerprint.computeHashInternally", "function_prototype": "void computeHashInternally(InputStreamGenerator)", "scope": "com.sourceclear.bytecode.v2.ClassFingerprint"}}, "flaw_match": {"procedure_hash": "2257002434", "prototype_hash": "1938546431", "flaw_hash": "4258098591", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "3520434851", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "org.slf4j.Logger.error", "issue_id": 1041, "gob": "B", "severity": 3, "issue_type_id": "taint", "issue_type": "Improper Output Neutralization for Logs", "cwe_id": "117", "display_text": "<span>This call to org.slf4j.Logger.error() could result in a log forging attack.  Writing untrusted data into a log file allows an attacker to forge log entries or inject malicious content into log files.  Corrupted log files can be used to cover an attacker's tracks or as a delivery mechanism for an attack on a log viewing or processing utility.  For example, if a web administrator uses a browser-based utility to review logs, a cross-site scripting attack might be possible. The first argument to error() contains tainted data. The tainted data originated from an earlier call to java.net.http.HttpClient.send.</span> <span>Avoid directly embedding user input in log files when possible.  Sanitize untrusted data used to construct log entries by using a safe logging mechanism such as the OWASP ESAPI Logger, which will automatically remove unexpected carriage returns and line feeds and can be configured to use HTML entity encoding for non-alphanumeric data.  Alternatively, some of the XSS escaping functions from the OWASP Java Encoder project will also sanitize CRLF sequences.  Only create a custom blocklist when absolutely necessary.  Always validate untrusted input to ensure that it conforms to the expected format, using centralized data validation routines when possible.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/117.html\">CWE</a> <a href=\"https://www.owasp.org/index.php/Log_injection\">OWASP</a> <a href=\"https://webappsec.pbworks.com/Improper-Output-Handling\">WASC</a> <a href=\"https://help.veracode.com/reader/4EKhlLSMHm5jC8P8j3XccQ/IiF_rOE79ANbwnZwreSPGA\">Supported Cleansers</a></span>", "files": {"source_file": {"file": "com/sourceclear/agent/wrapper/sourceclear/client/SourceClearClient.kt", "line": 50, "function_name": "veracodeScan", "qualified_function_name": "com.sourceclear.agent.wrapper.sourceclear.client.SourceClearClient.veracodeScan", "function_prototype": "github.kittinunf.result.Result veracodeScan(api.data.match.VeracodeScan, int, int)", "scope": "com.sourceclear.agent.wrapper.sourceclear.client.SourceClearClient"}}, "flaw_match": {"procedure_hash": "261729542", "prototype_hash": "864168156", "flaw_hash": "214593553", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4021447904", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "1626616545", "cause_hash2_ordinal": "1"}}, {"title": "javax.crypto.spec.IvParameterSpec.!operator_javan<PERSON><PERSON>t", "issue_id": 1002, "gob": "B", "severity": 3, "issue_type_id": "crypto", "issue_type": "Use of a Broken or Risky Cryptographic Algorithm", "cwe_id": "327", "display_text": "<span>Initialization vector being used here is not cryptographically strong for the underlying primitive's encryption output.</span> <span>Make sure its cryptographically generated using a good random number generator algorithm and seeded with OS generated entropy. Length of initialization vector should be same as the size of the underlying block on which the cipher works. For AES with GCM mode use a initialization vector of 96 bits, and for almost all other modes use 128 bits, for stream ciphers Salsa/ChaCha family initialization vector size should be 96 bits, and XSalsa and XChacha use 192 bits of vector size.</span> <span><BR><a href=\"https://csrc.nist.gov/Projects/block-cipher-techniques/BCM/current-modes\">Modes of block cipher</a><a href=\"https://tools.ietf.org/html/rfc7539\">ChaCha20-Poly1305 Specification</a></span>", "files": {"source_file": {"file": "com/sourceclear/agent/wrapper/service/utils/Cipher.kt", "line": 14, "function_name": "aesCipherWith", "qualified_function_name": "com.sourceclear.agent.wrapper.service.utils.CipherKt.aesCipherWith", "function_prototype": "javax.crypto.Cipher aesCipherWith(byte  [], byte  [], int)", "scope": "com.sourceclear.agent.wrapper.service.utils.CipherKt"}}, "flaw_match": {"procedure_hash": "3037151917", "prototype_hash": "768971381", "flaw_hash": "3484427132", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2866949028", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "javax.net.ssl.SSLContext.getInstance", "issue_id": 1003, "gob": "B", "severity": 3, "issue_type_id": "crypto", "issue_type": "Selection of Less-Secure Algorithm During Negotiation ('Algorithm Downgrade')", "cwe_id": "757", "display_text": "<span>SSL() cipher suite is enabled for used for communication between client and server, which is not secure. To ensure data being transmitted between client and server over TLS protocol is authenticated, encrypted and integrity protected, ensure only secure cipher suites are being used. </span><span>Make sure authenticated encryption mode (such as GCM) for block ciphers in key agreement protocol is being used. For Message Authentication codes, use an approved block cipher algorithm or codes computed based on SHA2 or SHA3 family of hashing algorithms.</span> <span><a href=\"https://cwe.mitre.org/data/definitions/757.html\">CWEID 757</a></span>", "files": {"source_file": {"file": "com/sourceclear/agent/wrapper/scanconfig/client/Client.kt", "line": 41, "function_name": "!ctor", "qualified_function_name": "com.sourceclear.agent.wrapper.scanconfig.client.Client.!ctor", "function_prototype": "void !ctor(java.lang.String, int, TwoWayTLSPair)", "scope": "com.sourceclear.agent.wrapper.scanconfig.client.Client"}}, "flaw_match": {"procedure_hash": "269068188", "prototype_hash": "3504311605", "flaw_hash": "2349647400", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1740685271", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "span.equ", "issue_id": 1108, "gob": "B", "severity": 2, "issue_type_id": "reliability", "issue_type": "Use of Wrong Operator in String Comparison", "cwe_id": "597", "display_text": "<span>Using '==' to compare two strings for equality actually compares the object references rather than their values.  It is unlikely that this reflects the intended application logic.</span> <span>Use the equals() method to compare strings, not the '==' operator.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/597.html\">CWE</a></span>", "files": {"source_file": {"file": "org/apache/commons/compress/harmony/unpack200/SegmentConstantPoolArrayCache.java", "line": 61, "function_name": "indexesForArrayKey", "qualified_function_name": "org.apache.commons.compress.harmony.unpack200.SegmentConstantPoolArrayCache.indexesForArrayKey", "function_prototype": "java.util.List indexesForArray<PERSON>ey(java.lang.String  [], java.lang.String)", "scope": "org.apache.commons.compress.harmony.unpack200.SegmentConstantPoolArrayCache"}}, "flaw_match": {"procedure_hash": "1649518425", "prototype_hash": "3264506181", "flaw_hash": "225286150", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "1690250470", "cause_hash_count": 2, "cause_hash_ordinal": 2, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "java.io.BufferedReader.!operator_javan<PERSON><PERSON>t", "issue_id": 1006, "gob": "B", "severity": 0, "issue_type_id": "reliability", "issue_type": "Improper Resource Shutdown or Release", "cwe_id": "404", "display_text": "<span>The program fails to release or incorrectly releases the variable reader, which was previously allocated by a call to java.io.BufferedReader.!operator_javanewinit().</span> <span>Ensure that all code paths properly release this resource.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/404.html\">CWE</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/yarn/CustomYarnLockParser.java", "line": 106, "function_name": "convertToValidYaml", "qualified_function_name": "com.sourceclear.engine.component.yarn.CustomYarnLockParser.convertToValidYaml", "function_prototype": "java.lang.String convertToValidYaml(java.io.InputStream)", "scope": "com.sourceclear.engine.component.yarn.CustomYarnLockParser"}}, "flaw_match": {"procedure_hash": "782224394", "prototype_hash": "1175464975", "flaw_hash": "2893105776", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2853736140", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "java.io.RandomAccessFile.!operator_javanew<PERSON>t", "issue_id": 1020, "gob": "B", "severity": 0, "issue_type_id": "reliability", "issue_type": "Improper Resource Shutdown or Release", "cwe_id": "404", "display_text": "<span>The program fails to release or incorrectly releases the variable file, which was previously allocated by a call to java.io.RandomAccessFile.!operator_javanewinit().</span> <span>Ensure that all code paths properly release this resource.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/404.html\">CWE</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/linuxso/ELFReader.java", "line": 172, "function_name": "!ctor", "qualified_function_name": "com.sourceclear.engine.component.linuxso.ELFReader.!ctor", "function_prototype": "void !ctor(java.io.File)", "scope": "com.sourceclear.engine.component.linuxso.ELFReader"}}, "flaw_match": {"procedure_hash": "2349957958", "prototype_hash": "2691615990", "flaw_hash": "2051716889", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "4127516739", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "java.io.BufferedReader.!operator_javan<PERSON><PERSON>t", "issue_id": 1005, "gob": "B", "severity": 0, "issue_type_id": "reliability", "issue_type": "Improper Resource Shutdown or Release", "cwe_id": "404", "display_text": "<span>The program fails to release or incorrectly releases the variable reader, which was previously allocated by a call to java.io.BufferedReader.!operator_javanewinit().</span> <span>Ensure that all code paths properly release this resource.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/404.html\">CWE</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/gem/GemfileDotLockParser.java", "line": 273, "function_name": "parse", "qualified_function_name": "com.sourceclear.engine.component.gem.GemfileDotLockParser.parse", "function_prototype": "void parse()", "scope": "com.sourceclear.engine.component.gem.GemfileDotLockParser"}}, "flaw_match": {"procedure_hash": "4195886312", "prototype_hash": "4146034803", "flaw_hash": "2654655326", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2102461906", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "java.io.BufferedReader.!operator_javan<PERSON><PERSON>t", "issue_id": 1004, "gob": "B", "severity": 0, "issue_type_id": "reliability", "issue_type": "Improper Resource Shutdown or Release", "cwe_id": "404", "display_text": "<span>The program fails to release or incorrectly releases the variable toLineStream, which was previously allocated by a call to java.io.BufferedReader.!operator_javanewinit().</span> <span>Ensure that all code paths properly release this resource.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/404.html\">CWE</a></span>", "files": {"source_file": {"file": "com/sourceclear/engine/component/collectors/GoModulesNativeCollector.java", "line": 446, "function_name": "toLineStream", "qualified_function_name": "com.sourceclear.engine.component.collectors.GoModulesNativeCollector.toLineStream", "function_prototype": "java.util.stream.Stream toLineStream(java.lang.String)", "scope": "com.sourceclear.engine.component.collectors.GoModulesNativeCollector"}}, "flaw_match": {"procedure_hash": "2342557429", "prototype_hash": "775239077", "flaw_hash": "1749635706", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "2853736140", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "management:endpoints:web:exposure:include:", "issue_id": 1001, "gob": "B", "severity": 4, "issue_type_id": "crypto", "issue_type": "Exposed Dangerous Method or Function", "cwe_id": "749", "display_text": "<span>The application contains dangerous administrative functionality which is enabled via the management:endpoints:web:exposure:include: configuration property. An attacker could use the exposed methods to perform sensitive operations on the application and leverage it to execute sophisticated attacks. These can amount to Denial-of-Service or tampering with application run-time behavior.</span> <span>Ensure that any administrative features or sensitive operations are not enabled in production if they are not needed. Set  to a value that prevents unathorized access or put other mitigating controls in place (e.g. network firewall rules) to prevent access by unauthorized parties. Ensure that proper authentication and authorization controls are in place for any required sensitive operations.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/749.html\">CWE</a></span>", "files": {"source_file": {"file": "BOOT-INF/classes/application.yml", "line": 16, "function_name": "application", "qualified_function_name": "application.application.application", "function_prototype": "void application(void)", "scope": "application.application"}}, "flaw_match": {"procedure_hash": "**********", "prototype_hash": "211846691", "flaw_hash": "836391459", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "**********", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}, {"title": "management:endpoint:health:show-details:", "issue_id": 1000, "gob": "B", "severity": 2, "issue_type_id": "crypto", "issue_type": "Information Exposure Through Sent Data", "cwe_id": "201", "display_text": "<span>While processing an exception, the application calls the management:endpoint:health:show-details:() function, which may expose sensitive information about the application.  This information can be useful in executing other attacks and can also enable the attacker to target known vulnerabilities in application components. </span> <span>Capture exceptions for debugging purposes, but ensure that only generic error messages are returned to the end user that do not reveal any additional details.</span> <span>References: <a href=\"https://cwe.mitre.org/data/definitions/201.html\">CWE</a></span>", "files": {"source_file": {"file": "BOOT-INF/classes/application.yml", "line": 1, "function_name": "application", "qualified_function_name": "application.application.application", "function_prototype": "void application(void)", "scope": "application.application"}}, "flaw_match": {"procedure_hash": "**********", "prototype_hash": "211846691", "flaw_hash": "**********", "flaw_hash_count": 1, "flaw_hash_ordinal": 1, "cause_hash": "**********", "cause_hash_count": 1, "cause_hash_ordinal": 1, "cause_hash2": "0", "cause_hash2_ordinal": "0"}}], "pipeline_scan": "21.7.0-0", "project_name": "agent-wrapper", "project_uri": "\"https://gitlab.laputa.veracode.io/sca/agent-wrapper\"", "project_ref": "\"sga-2665\"", "dev_stage": "DEVELOPMENT"}