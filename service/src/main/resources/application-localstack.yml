spring:
  cloud:
    aws:
      endpoint: "http://localhost:4566"
      region:
        static: "us-east-1"
      credentials:
        # Fake keys
        access-key: "ASIA3H3OFLO6E5ZICKLQ"
        secret-key: "9gZM4lXylAW/Hg+2BBll4xPrZnDrRnlBV65Y6fZ1"
      s3:
        endpoint: "http://s3.localhost.localstack.cloud:4566"
      sqs:
        endpoint: "http://localhost:4566"
      #
      # kms property here is made just for localstack
      #
      kms:
        endpoint: "http://localhost:4566"