package com.sourceclear.agent.wrapper.service.queue.unpackers

import com.sourceclear.agent.wrapper.service.queue.unpackers.UnpackRecursive.Companion.unpackDirectoryName
import com.sourceclear.agent.wrapper.service.queue.unpackers.UnpackRecursive.Companion.unpackRecursively
import com.sourceclear.agent.wrapper.service.utils.CloseableTempIO
import com.sourceclear.agent.wrapper.service.utils.Constants
import org.junit.jupiter.api.Test
import java.nio.file.Files
import java.nio.file.Paths
import kotlin.test.assertEquals
import kotlin.test.assertTrue

internal class UnpackRecursiveKtTest {

  @Test
  fun testUnpackRecursively() {
    val file = Paths.get(UnpackRecursiveKtTest::class.java.classLoader.getResource("simple.zip")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      Files.copy(file, it.resolve(file.fileName))
      unpackRecursively(it, 3, Constants.MAX_DIR_DEPTH)
      val newName = it.resolve(unpackDirectoryName(file.fileName.toString()))
      assertTrue { Files.exists(newName) }
      assertTrue { Files.exists(newName.resolve(Paths.get("PHP.php"))) }
    }
  }

  /**
   * This test should finish successfully without throwing exception.
   */
  @Test
  fun testUnpackRecursivelyBadZip() {
    // bad.zip has filenames with null in them, which would cause exception to be thrown.
    val file = Paths.get(UnpackRecursiveKtTest::class.java.classLoader.getResource("bad.zip")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      Files.copy(file, it.resolve(file.fileName))
      unpackRecursively(it, 2, Constants.MAX_DIR_DEPTH)
    }
  }

  /**
   * This test should finish successfully without throwing exception.
   */
  @Test
  fun testUnpackRecursivelyBadZip2() {
    // bad2.zip triggers EOFException when unzipped
    val file = Paths.get(UnpackRecursiveKtTest::class.java.classLoader.getResource("bad2.zip")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      Files.copy(file, it.resolve(file.fileName))
      unpackRecursively(it, 2, Constants.MAX_DIR_DEPTH)
    }
  }

  @Test
  fun testComplexUnpackRecursively() {
    val file = Paths.get(UnpackRecursiveKtTest::class.java.classLoader.getResource("simple.zip")!!.toURI())
    val filenames = setOf(file.fileName.toString(), "1" + file.fileName.toString())
    CloseableTempIO.createTempDirectory("test").use {
      filenames.forEach { filename ->
        Files.copy(file, it.resolve(filename))
        unpackRecursively(it, 3, Constants.MAX_DIR_DEPTH)
        val newName = it.resolve(unpackDirectoryName(filename))
        assertTrue { Files.exists(newName) }
        assertTrue { Files.exists(newName.resolve(Paths.get("PHP.php"))) }
      }
    }
  }

  /**
   * We should extract .dll files, and only .dll files from a Nuget archive
   */
  @Test
  fun testUnpackRecursivelyNuget() {
    val nugetArchive = Paths.get(UnpackRecursiveKtTest::class.java.classLoader.getResource("test.nupkg")!!.toURI())
    var fileCount = 0

    CloseableTempIO.createTempDirectory("test").use {
      Files.copy(nugetArchive, it.resolve(nugetArchive.fileName))
      unpackRecursively(it, 1, Constants.MAX_DIR_DEPTH)
      val unpackDirectory = it.resolve(unpackDirectoryName("test.nupkg"))
      for (file in unpackDirectory.toFile().walkTopDown()) {
        if (file.isDirectory) {
          continue
        }
        assertTrue(file.name.toLowerCase().endsWith(".dll"))
        fileCount++
      }
    }

    assertEquals(1, fileCount)
  }

}