# Agent Wrapper Service

This service receives scan jobs from SQS, downloads the binaries, scans, and sends the results to the sourceclear platform.

## What does this service do?

![](arch-diagram.png)

The agent wrapper service is the blue component in the above diagram. It
receives scan request messages from the scan config service (numbered 6 and 7
in the diagram) through SQS and it sends the evidence to the SourceClear
Platform (9 in the diagram).

## Stand up local environment to run Agent Wrapper

Execute the [setupLocalEnvForAgentWrapper.sh](./localstack/setupLocalEnvForAgentWrapper.sh). It will bring up localstack via docker compose and run terraform to create aws resources in the localstack.
You can destroy the resources created by setupLocalEnvForAgentWrapper.sh by running the [destroyLocalEnvForAgentWrapper.sh](./localstack/destroyLocalEnvForAgentWrapper.sh).

In IntelliJ set the Spring Boot run configuration and set the spring boot profile to localstack:
![intelliJRunConfig.png](images/intelliJRunConfig.png)

### ScanRequestMessage

Scan request messages come in the form of JSON that look something like this:

```json
{
  "encrypt_iv": "deadbeef",
  "encrypt_key": "cafebabe",
  "account_id": 15665,
  "binary_path": "path/to/binaries/on/s3/",
  "encrypted_key": "deadbeefcafebabe',
  "bucket_name": "s3_bucket_name",
  "scan_id":"ded6831d-e965-453f-911a-0d3def24152a",
  "env":"qa-18",
  "app_id":54378,
  "app_ver_id":53991,
  "scan_config_id":"1c5d6830-452d-46d7-8205-87b67d5d0bc7"
}

```

The JSON messages are then deserialized into the
[`ScanRequestMessage`](objects/src/main/java/com/sourceclear/agent/wrapper/objects/ScanRequestMessage.java)
class.

```kotlin
data class ScanRequestMessage(

  // The hex encoded encryption iv that was used to encrypt the binary.
  val encryptIv: String?,

  // The hex encoded encryption key that was used to encrypt the binary.
  val encryptKey: String?,


  // The s3 where the binaries are residing in
  val binaryPath: String,

  /*
    The encrypted key is a hex encoded and KMS encrypted string that consists
    of the binary form of the encryption key and IV concatenated into a single
    byte array. It is created this way in pseudo code:

        encryptedKey = hexEncode(kmsEncrypt(binKey + binIv))

  */
  val encryptedKey: String?,

  // The bucket name where the binaries reside in.
  val bucketName: String,

  // Scan related fields. The values doesn't really matter in the context of the agent wrapper.
  val accountId: Long,
  val scanId: String,
  val env: String?,
  val appId: Long,
  val appVerId: Long,
  val scanConfigId: String)
```

#### Encryption IV and key

The `encryptIv` and `encryptKey` is used for debugging. When debugging, these
keys are sent to the agent wrapper without KMS encryption. The sender just need
to send these in hex encoded plaintext. Usually the output from `openssl enc`
would suffice.

In a production environment, the `encryptIv` and `encryptKey` values would be
`null` and the `encryptedKey` would be used. This value is a hex encoded, KMS
encrypted string. The first 32 bytes are the encryption key and the last 16
bytes are the IV.

See https://wiki.veracode.local/pages/viewpage.action?spaceKey=Eng&title=Static+Scans+AWS+KMS+Based+Encryption+Support

<sup>Sigh... This scheme sucks, I know. It's generated by the folks at the static cloud engine...</sup>

#### Envelope Encryption

The binary and its encryption keys are protected using [envelope
encryption](https://docs.aws.amazon.com/kms/latest/developerguide/concepts.html#enveloping).
The service first has to hex decode the keys in the scan request message,
and
then decrypt the decoded keys with KMS. Once decrypted, then the service would
be able to decrypt the binary using the decrypted key.

### Encrypting a test binary for testing

Use the following commands to encrypt the binary using the `openssl` command.
```
openssl enc -nosalt -aes-256-cbc -P
# enter something as the password and assign the printed key and iv to $key and $iv

openssl enc -aes-256-cbc -K $key -iv $iv -in path/to/test/binary.zip  -out encrypted.zip
```

Then encrypt the key and iv using AWS KMS and upload the encrypted binary to S3.

```
aws kms encrypt --key-id $kms-key-id --plaintext ($key + $iv)
```

### Automation Script

Alternatively, you may use the `bin/scan.rb` script to automate the whole process like so:

```
ruby bin/scan.rb ~/repos/example-ruby.zip
```

Simply pass it the path to a binary and the script will encrypt, upload, and
trigger a scan on sqs for you.

The script will default to a SQS queue, a S3 bucket, and a KMS master key if no
command line options are specified. If you prefer to use your own AWS queue,
bucket, or KMS master, key, just invoke the script with the following flags:

```
ruby bin/scan.rb --queue jsyeo-scan-request --bucket jyeo-test --key alias/sca-nonprod-dev-master-key ~/repos/example-ruby.zip
```

#### Requirements

To use the script you need these:
- ruby
- awscli (with your aws credentials defined in the environment variables or in `~/.aws/credentials`)
- openssl

### Scanning a Test Binary Using Integration Testing Framework

An alternative approach for scanning a test binary is to utilize the
integration test suite implemented in
`QueueHandlerIntegrationTest.kt`. The steps are:

1. Put the binary (Zip, War, Ear, or Jar expected) in
   `service/src/test/resources/integration`.

2. Annotate the method `scan capture` in
   `QueueHandlerIntegrationTest.kt` with `@Test`.

3. Run `mvn test`. In addition to running the usual tests, this will
   also scan the test binary and put the evidence as a JSON file in
   the `service/src/test/resources/integration` directory.

<sup>Made with❤️ in Singapore</sup>
