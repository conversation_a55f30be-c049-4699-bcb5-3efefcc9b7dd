package com.sourceclear.agent.wrapper.service.queue.unpackers

import com.sourceclear.agent.wrapper.service.queue.QueueHandler
import com.sourceclear.api.data.evidence.CoordinateType
import com.sourceclear.api.data.evidence.Coordinates
import com.sourceclear.api.data.evidence.Evidence
import com.sourceclear.api.data.evidence.EvidencePath
import com.sourceclear.api.data.evidence.EvidenceType
import org.junit.jupiter.api.Test
import java.nio.file.Paths
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertTrue

class QueueHandlerTest {

  @Test
  fun testFilterDuplicatesEvidencePath() {
    val evidenceSet = HashSet<Evidence>()
    evidenceSet.add(Evidence.Builder()
      .withEvidenceId("591f7464-ef50-4e52-ba00-6145b7ccefec")
      .withEvidencePaths(listOf(
        EvidencePath("war-in-war.zip$UNPACK_DIR_SUFFIX/dir1/binaries/mywar1.2.3.war${UNPACK_DELIMITER}WEB-INF/lib/commons-pool-1.5.4.jar", 1, listOf()),
        EvidencePath("war-in-war.zip$UNPACK_DIR_SUFFIX/dir1/binaries/mywar1.war${UNPACK_DELIMITER}WEB-INF/lib/commons-pool-1.5.4.jar", 1, listOf()),
        EvidencePath("war-in-war.zip$UNPACK_DIR_SUFFIX/dir1/binaries/mywar1.2.3.war$UNPACK_DIR_SUFFIX/WEB-INF/lib/commons-pool-1.5.4.jar", 1, listOf()),
        EvidencePath("war-in-war.zip$UNPACK_DIR_SUFFIX/dir1/binaries/mywar1.war$UNPACK_DIR_SUFFIX/WEB-INF/lib/commons-pool-1.5.4.jar", 1, listOf())
      ))
      .withEvidenceType(EvidenceType.BYTECODE)
      .build())

    val filteredEvidenceSet = QueueHandler.deduplicateEvidence(QueueHandler.normalizeAllPaths(evidenceSet))
    assertNotNull(filteredEvidenceSet)
    assertTrue { filteredEvidenceSet.size == 1 }
    assertTrue { filteredEvidenceSet.first().evidencePaths.size == 2 }
    assertTrue { filteredEvidenceSet.first().evidencePaths.get(0).filePath.equals("war-in-war.zip${UNPACK_DELIMITER}dir1/binaries/mywar1.2.3.war${UNPACK_DELIMITER}WEB-INF/lib/commons-pool-1.5.4.jar") }
    assertTrue { filteredEvidenceSet.first().evidencePaths.get(1).filePath.equals("war-in-war.zip${UNPACK_DELIMITER}dir1/binaries/mywar1.war${UNPACK_DELIMITER}WEB-INF/lib/commons-pool-1.5.4.jar") }
  }

  @Test
  fun testEliminateDupEvidencePathsInEvidenceSet() {
    val evidenceSet = HashSet<Evidence>()
    evidenceSet.add(Evidence.Builder()
      .withEvidenceId("b0eef2f4-a766-40ae-8274-c0c501b0a010")
      .withEvidencePaths(listOf(
        EvidencePath("war-in-war.zip${UNPACK_DELIMITER}dir1/binaries/mywar1.war${UNPACK_DELIMITER}WEB-INF/lib/commons-pool-1.5.4.jar:::org/apache/commons/pool/impl", 1, listOf())
      ))
      .withEvidenceType(EvidenceType.BYTECODE)
      .build())
    evidenceSet.add(Evidence.Builder()
      .withEvidenceId("cc8601dc-b2c6-4028-9151-a7dbb923b501")
      .withEvidencePaths(listOf(
        EvidencePath("war-in-war.zip$UNPACK_DIR_SUFFIX/dir1/binaries/mywar1.war$UNPACK_DIR_SUFFIX/WEB-INF/lib/commons-pool-1.5.4.jar:::org/apache/commons/pool/impl", 1, listOf())
      ))
      .withEvidenceType(EvidenceType.BYTECODE)
      .build())

    val filteredEvidenceSet = QueueHandler.deduplicateEvidence(QueueHandler.normalizeAllPaths(evidenceSet))
    assertNotNull(filteredEvidenceSet)
    /*
     * Why assert for size 1?
     * Two evidences with almost similar evidence path where the difference is mywar1.war#zip and mywar1_war
     * filterPossibleDuplicateArchivesInEvidence() marks the second evidence as duplicate of the first
     * and eliminates it from the returned evidence set.
     */
    assertTrue { filteredEvidenceSet.size == 1 }
    assertTrue { filteredEvidenceSet.first().evidencePaths[0].filePath.equals("war-in-war.zip${UNPACK_DELIMITER}dir1/binaries/mywar1.war${UNPACK_DELIMITER}WEB-INF/lib/commons-pool-1.5.4.jar:::org/apache/commons/pool/impl") }
  }

  @Test
  fun testNoChange() {
    val evidenceSet = HashSet<Evidence>()

    evidenceSet.add(Evidence.Builder()
      .withEvidenceId("84f58379-1bcd-4173-bc3b-5ffd98cf40a6")
      .withEvidencePaths(listOf(
        EvidencePath("war-in-war_zip/dir1/binaries/mywar1.2.3.war", 1, listOf()),
        EvidencePath("war-in-war_zip/dir1/binaries/mywar1.war", 1, listOf())
      ))
      .withEvidenceType(EvidenceType.JAR)
      .build())

    val filteredEvidenceSet = QueueHandler.deduplicateEvidence(QueueHandler.normalizeAllPaths(evidenceSet))
    assertNotNull(filteredEvidenceSet)
    assertTrue { filteredEvidenceSet.size == 1 }
    val filteredEvidenceList = filteredEvidenceSet.toList()
    val evidence1 = filteredEvidenceList[0]

    assertTrue { evidence1.evidencePaths.size == 2 }
    assertTrue { evidence1.evidencePaths[0].filePath.equals("war-in-war_zip/dir1/binaries/mywar1.2.3.war") }
    assertTrue { evidence1.evidencePaths[1].filePath.equals("war-in-war_zip/dir1/binaries/mywar1.war") }
  }

  @Test
  fun testReplaceUnoackDirSuffixWithHashZip() {
    val evidenceSet = HashSet<Evidence>()

    evidenceSet.add(Evidence.Builder()
      .withEvidenceId("415b60c9-c3bc-47b0-ad1e-6d9e228b6288")
      .withEvidencePaths(listOf(
        EvidencePath("war-in-war.zip$UNPACK_DIR_SUFFIX/dir1/binaries/mywar1.2.3$UNPACK_DIR_SUFFIX/WEB-INF/lib/commons-pool-1.5.4.jar:::org/apache/commons/pool/impl", 1, listOf())
      ))
      .withEvidenceType(EvidenceType.BYTECODE)
      .build())

    val filteredEvidenceSet = QueueHandler.deduplicateEvidence(QueueHandler.normalizeAllPaths(evidenceSet))
    assertNotNull(filteredEvidenceSet)
    assertTrue { filteredEvidenceSet.size == 1 }
    assertTrue { filteredEvidenceSet.first().evidencePaths[0].filePath.equals("war-in-war.zip${UNPACK_DELIMITER}dir1/binaries/mywar1.2.3${UNPACK_DELIMITER}WEB-INF/lib/commons-pool-1.5.4.jar:::org/apache/commons/pool/impl") }
  }

  /**
   * This tests that {@link EvidenceType.BYTECODE} evidence are merged, but {@link EvidenceType.COORDINATES} are
   * not, even though they have the same path.
   */
  @Test
  fun testPyPIEvidencePreservation() {
    val azureCommon = Evidence.Builder()
      .withCoordinates(Coordinates.Builder()
        .withCoordinate1("azure_common")
        .withVersion("1.1.24")
        .withCoordinateType(CoordinateType.PYPI)
        .build())
      .withEvidenceType(EvidenceType.COORDINATES)
      .withEvidencePath(EvidencePath("Pipfile.lock", 1, listOf()))
      .build();

    val wcwidth = Evidence.Builder()
      .withCoordinates(Coordinates.Builder()
        .withCoordinate1("wcwidth")
        .withVersion("0.1.8")
        .withCoordinateType(CoordinateType.PYPI)
        .build())
      .withEvidenceType(EvidenceType.COORDINATES)
      .withEvidencePath(EvidencePath("Pipfile.lock", 19, listOf()))
      .build();

    val propertyEditors1 = Evidence.Builder()
      .withByteCodeHash("deadbeef")
      .withSha1("cafebabe")
      .withSha2("dabadaba")
      .withEvidencePath(
        EvidencePath("module-web-1.0-SNAPSHOT.war${UNPACK_DELIMITER}WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/propertyeditors", 1, listOf())
      )
      .withEvidenceType(EvidenceType.BYTECODE)
      .build();

    val propertyEditors2 = Evidence.Builder()
      .withByteCodeHash("deadbeef")
      .withSha1("cafebabe")
      .withSha2("dabadaba")
      .withEvidencePath(
        EvidencePath("module-web-1.0-SNAPSHOT.war$UNPACK_DIR_SUFFIX/WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar:::org/springframework/beans/propertyeditors", 1, listOf())
      )
      .withEvidenceType(EvidenceType.BYTECODE)
      .build();

    val evidence = setOf<Evidence>(azureCommon, wcwidth, propertyEditors1, propertyEditors2)
    val filteredEvidence = QueueHandler.deduplicateEvidence(QueueHandler.normalizeAllPaths(evidence))

    assertEquals(3, filteredEvidence.size)
    assertTrue(filteredEvidence.contains(azureCommon))
    assertTrue(filteredEvidence.contains(wcwidth))
    assertTrue(filteredEvidence.contains(propertyEditors1) || filteredEvidence.contains(propertyEditors2))
  }

  @Test
  fun testJarPathDeduplication() {
    val evidence1 = Evidence.Builder()
      .withByteCodeHash("deadbeef")
      .withSha1("cafebabe")
      .withSha2("dabadaba")
      .withEvidencePaths(
        listOf(
          EvidencePath(
            "module-ear-1.0-SNAPSHOT.ear${UNPACK_DELIMITER}module-web-1.0-SNAPSHOT.war${UNPACK_DELIMITER}WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar",
            1,
            listOf()
          ),
          EvidencePath("module-web-1.0-SNAPSHOT.war${UNPACK_DELIMITER}WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar", 1, listOf()),
          EvidencePath(
            "module-ear-1.0-SNAPSHOT.ear$UNPACK_DIR_SUFFIX/module-web-1.0-SNAPSHOT.war${UNPACK_DELIMITER}WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar",
            1,
            listOf()
          ),
          EvidencePath("module-web-1.0-SNAPSHOT.war$UNPACK_DIR_SUFFIX/WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar", 1, listOf()),
          EvidencePath(
            "module-ear-1.0-SNAPSHOT.ear$UNPACK_DIR_SUFFIX/module-web-1.0-SNAPSHOT.war$UNPACK_DIR_SUFFIX/WEB-INF/lib/spring-jcl-5.1.2.RELEASE.jar",
            1,
            listOf()
          )
        )
      )
      .withEvidenceType(EvidenceType.JAR)
      .build();

    val evidence2 = Evidence.Builder()
      .withByteCodeHash("deadbeef")
      .withSha1("cafebabe")
      .withSha2("dabadaba")
      .withEvidencePaths(
        listOf(
          EvidencePath(
            "module-ear-1.0-SNAPSHOT.ear${UNPACK_DELIMITER}module-web-1.0-SNAPSHOT.war${UNPACK_DELIMITER}WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar",
            1,
            listOf()
          ),
          EvidencePath("module-web-1.0-SNAPSHOT.war${UNPACK_DELIMITER}WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar", 1, listOf())
        )
      )
      .withEvidenceType(EvidenceType.JAR)
      .build();

    val evidence3 = Evidence.Builder()
      .withByteCodeHash("deadbeef")
      .withSha1("cafebabe")
      .withSha2("dabadaba")
      .withEvidencePaths(
        listOf(
          EvidencePath(
            "module-ear-1.0-SNAPSHOT.ear$UNPACK_DIR_SUFFIX/module-web-1.0-SNAPSHOT.war${UNPACK_DELIMITER}WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar",
            1,
            listOf()
          ),
          EvidencePath("module-web-1.0-SNAPSHOT.war$UNPACK_DIR_SUFFIX/WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar", 1, listOf()),
          EvidencePath(
            "module-ear-1.0-SNAPSHOT.ear$UNPACK_DIR_SUFFIX/module-web-1.0-SNAPSHOT.war$UNPACK_DIR_SUFFIX/WEB-INF/lib/spring-beans-5.1.2.RELEASE.jar",
            1,
            listOf()
          )
        )
      )
      .withEvidenceType(EvidenceType.JAR)
      .build();

    val evidence = setOf<Evidence>(evidence1, evidence2, evidence3)
    val filteredEvidence = QueueHandler.deduplicateEvidence(QueueHandler.normalizeAllPaths(evidence)).toList()

    assertEquals(2, filteredEvidence.size)
    assertEquals(filteredEvidence[0].evidencePaths.size, 2)
    assertEquals(filteredEvidence[1].evidencePaths.size, 2)
  }

  @Test
  fun testNoDLLScanInNodeModules() {
    val targetDir = Paths.get(this.javaClass.getResource("/node_modules_with_dll").toURI())
    val evidenceSet = QueueHandler.scan(targetDir)

    // We should not pick up anything with bytecode hash
    assertTrue(evidenceSet.map { it.bytecodeHash }.isEmpty())
  }

}