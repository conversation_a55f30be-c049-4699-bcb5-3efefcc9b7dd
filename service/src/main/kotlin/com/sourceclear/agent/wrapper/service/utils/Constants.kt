package com.sourceclear.agent.wrapper.service.utils

interface Constants {
  companion object {

    /**
     * Google bundle tool path Sprint property
     */
    const val BUNDLETOOL_PATH_SPRING_PROP = "bundletool.path"

    /**
     * Google bundle tool path system property
     */
    const val BUNDLETOOL_PATH_SYSTEM_PROP = "BUN<PERSON><PERSON>TOOL_PATH"

    /**
     * APKs extension
     */
    const val APKS_EXTENSION = "apks"

    /**
     * Default universal APK file name
     */
    const val UNIVERSAL_APK = "universal.apk"

    /**
     * The maximum directory depth to look for archives
     */
    const val MAX_DIR_DEPTH = 3

    /**
     * The directory search depth for Android package, such as, apk, aar, aab
     */
    const val MAX_DIR_DEPTH_ANDROID = 5;

    /**
     * Android package extensions
     */
    val ANDROID_PACKAGE_EXTENSIONS = setOf<String>("apk", "aar", "aab")

    /**
     * Plexus command line timeout
     */
    const val COMMAND_LINE_TIMEOUT = 60
  }
}