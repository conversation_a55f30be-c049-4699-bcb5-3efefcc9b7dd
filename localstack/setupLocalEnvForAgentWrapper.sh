#!/bin/bash
#
# Set up aws resources via localstack for agent-wrapper to run locally
#

#
# Verify run from <ProjectRoot>/localstack directory
#
if [[ `pwd` != *"agent-wrapper/localstack" ]]; then
  echo "ERROR: Please rerun script from agent-wrapper/localstack directory (ie, ../agent-wrapper/localstack)" >&2
  exit 1
fi

#
# Run docker compose (docker-compose.yml) to bring up localstack
#
docker compose up -d

if [[ $? -eq 0 ]]; then
  echo "INFO: Successfully ran localstack via docker compose"
else
  echo "ERROR: localstack run via docker compose unsuccessful" >&2
  exit 1
fi

#
# Run terraform
#
if [[ -x "$(command -v terraform)" ]]; then
  echo "INFO: Using terraform located at $(command -v terraform)"
else
  echo "ERROR: terraform is not installed" >&2
  exit 1
fi
cd terraform
read -p 'Clean previous terraform state at ./terraform dir? Type yes or y to clean previous tf state: ' CLEAN_TF_STATE
if [[ "$CLEAN_TF_STATE" =~ ^y(es)?$ ]]; then
  rm .terraform.lock.hcl && rm terraform.tfstate
fi
terraform init
terraform apply
exit 0

