# Changelog entries

This guide contains instructions for how to generate a changelog entry file.

## Changelog files

Unreleased changelog files should be created for each merge request in the
srcclr-agent repository. These files are yaml files that are to be stored in
the [changelogs/unreleased/](unreleased) directory.

The YAML files is expected to be in the following format:

```yaml
---
title: 'Fix C++ scanning in windows'
merge_request: sourceclear/engines!123
ticket: SCAA-1234
author: <PERSON>
type: fixed
```

- `title`: A descriptive and concise title for the change.
- `merge_request`: The merge request for the change. If the merge request is in another repo, use the `<org>/<repo>!<merge_request_id>` format to reference the merge request.
- `ticket`: The jira ticket for the change.
- `author`: The author who made the change.
- `type`: The type of change, the type can be either `added`, `fixed`, `changed`, `deprecated`, `removed`, `security`, `performance`, or `other`.

## Changelog script

To automate the creation of these yaml files we have a changelog script in
`bin/changelog` that generates these files in the `changelogs/unreleased`
directory. This script is adapted from gitlab's changelog script.

```
$ bin/changelog 'My cool feature that I've added to srcclr'
```

This would create a yaml file with the supplied argument as its title in the changelog directory. The entry filename is based on the name of the current git branch.

### Usage

```
$ bin/changelog --help
Usage: ./bin/changelog [options] [title]

        --amend                      Amend the previous commit
    -f, --force                      Overwrite an existing entry
    -m, --merge-request [integer]    Merge Request ID
    -n, --dry-run                    Don't actually write anything, just print
    -u, --git-username               Use Git user.name configuration as the author
    -j, --jira [string]              The jira ticket of the change
    -t, --type [string]              The category of the change, valid options are: added, fixed, changed, deprecated, removed, security, performance, other
    -h, --help                       Print help message

```

See gitlab's changelog script
[docs](https://gitlab.com/gitlab-org/gitlab-ce/blob/master/doc/development/changelog.md)
for details beyond what is described here.

## CHANGELOG.md generation

The `CHANGELOG.md` file is generated by the `bin/generate-changelog` script.
This script will automatically collate the yaml files and convert them into
markdown and prepend the changelog into the existing `CHANGELOG.md` file.

The script will also delete the changelog files in `changelogs/unreleased` and
commits the changes to git.
