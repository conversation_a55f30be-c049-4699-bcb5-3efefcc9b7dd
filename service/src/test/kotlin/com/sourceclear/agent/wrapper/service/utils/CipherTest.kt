package com.sourceclear.agent.wrapper.service.utils

import org.apache.commons.codec.binary.Hex
import org.junit.Test
import java.nio.ByteBuffer
import java.security.DigestInputStream
import java.security.MessageDigest
import javax.crypto.CipherInputStream
import kotlin.test.assertEquals


class CipherTest {
  @Test
  fun testAesFileDecyption() {
    val hex = Hex()
    val key = hex.decode(ByteBuffer.wrap("27e08ca1edec290fccd8e69b29b7b5784c9de82924dfac2bb3bbfdcddb5e0843".toByteArray()))
    val iv = hex.decode(ByteBuffer.wrap("f7d7a723a479d0ae32454eff472a0282".toByteArray()))
    val cipher = aesCipherWith(key, iv)
    val stream = CipherTest::class.java.classLoader.getResourceAsStream("commons-fileupload-1.2.jar.aes")
    val digest = MessageDigest.getInstance("SHA-256")

    val sha2 = CipherInputStream(stream, cipher).use { cipherStream ->
      DigestInputStream(cipherStream, digest).use {
        it.readAllBytes()
        it.messageDigest.digest()
      }
    }

    // assert that we got the right decrypted binary by comparing its sha2s
    assertEquals("b570cacc936c8b6ed4b7741219de8782a6a796c6f929e97625a888847a8df1f3", Hex.encodeHexString(sha2))
  }
}
