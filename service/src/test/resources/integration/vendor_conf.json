[{"evidenceId": "5116bc03-1f43-4306-9bcf-9a9093c8d06a", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 9, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L9"}], "coordinates": {"coordinate1": "github.com/mattn/go-isatty", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "30a891c33c7cde7b02a981314b4228ec99380cca", "evidenceType": "COMMIT"}, {"evidenceId": "3c5f655a-dcdd-4811-a611-d26274e91a43", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 5, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L5"}], "coordinates": {"coordinate1": "github.com/simeji/jid", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "9090f06e9643aa1a4e95904a4ebbd6d186b3a20f", "evidenceType": "COMMIT"}, {"evidenceId": "184af166-d22c-4a66-89b6-65866e6a9860", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 4, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L4"}], "coordinates": {"coordinate1": "github.com/google/go-github", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "173908142398084e14d7f14c39924f0004d43679", "evidenceType": "COMMIT"}, {"evidenceId": "b49503d3-6fc7-4efa-a882-cba85fd2d69e", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 11, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L11"}], "coordinates": {"coordinate1": "github.com/nsf/termbox-go", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "abe82ce5fb7a42fbd6784a5ceb71aff977e09ed8", "evidenceType": "COMMIT"}, {"evidenceId": "8255aafd-0d52-4555-b3b9-073569d28b81", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 3, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L3"}], "coordinates": {"coordinate1": "github.com/google/go-querystring", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "53e6ce1", "evidenceType": "COMMIT"}, {"evidenceId": "c4de5483-72ca-4a9f-98a7-2fe5c1db153a", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 8, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L8"}], "coordinates": {"coordinate1": "github.com/mattn/go-colorable", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "d228849504861217f796da67fae4f6e347643f15", "evidenceType": "COMMIT"}, {"evidenceId": "0dce6ea5-b474-427a-ac8a-b288ff998676", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 14, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L14"}], "coordinates": {"coordinate1": "golang.org/x/sys", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "d75a52659825e75fff6158388dddc6a5b04f9ba5", "evidenceType": "COMMIT"}, {"evidenceId": "4f33bd95-9e43-458b-bade-7706da018db3", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 6, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L6"}], "coordinates": {"coordinate1": "github.com/bitly/go-simplejson", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "aabad6e819789e569bd6aabf444c935aa9ba1e44", "evidenceType": "COMMIT"}, {"evidenceId": "ea8e0733-38c7-4d02-87f4-f6400e8a2228", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 12, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L12"}], "coordinates": {"coordinate1": "github.com/nwidger/jsoncolor", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "0192e84d44af834c3a90c8a17bf670483b91ad5a", "evidenceType": "COMMIT"}, {"evidenceId": "7a1cb27f-ce5d-43a2-8fa6-bcd2b60cb07f", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 15, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L15"}], "coordinates": {"coordinate1": "github.com/square/go-jose", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "7f0dd807d3f3d73bb536898cb7980ddf638ce69a", "evidenceType": "COMMIT"}, {"evidenceId": "5bb19997-22e5-4a32-8acb-31cdfc36960a", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 13, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L13"}], "coordinates": {"coordinate1": "github.com/pkg/errors", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "248dadf4e9068a0b3e79f02ed0a610d935de5302", "evidenceType": "COMMIT"}, {"evidenceId": "7b2541af-e69a-47fd-ba4d-0d1e4ff62653", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 10, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L10"}], "coordinates": {"coordinate1": "github.com/mattn/go-runewidth", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "737072b4e32b7a5018b4a7125da8d12de90e8045", "evidenceType": "COMMIT"}, {"evidenceId": "a09e09fe-5693-4e21-b84f-162f3c91d632", "evidencePaths": [{"filePath": "vendor_conf.zip#zip:vendor.conf", "lineNumber": 7, "dependencyPath": [], "filePathWithLineNumber": "vendor_conf.zip#zip:vendor.conf#L7"}], "coordinates": {"coordinate1": "github.com/fatih/color", "coordinate2": null, "version": "HEAD", "platform": null, "scope": null, "coordinateType": "GO"}, "sha1": null, "sha2": null, "bytecodeHash": null, "commitHash": "42c364ba490082e4815b5222728711b3440603eb", "evidenceType": "COMMIT"}]