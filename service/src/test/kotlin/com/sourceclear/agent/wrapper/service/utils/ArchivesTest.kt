package com.sourceclear.agent.wrapper.service.utils

import com.sourceclear.engine.component.collectors.npm.internal.NpmFileAnalyser.NPMFilePatterns.SHRINKWRAP_FILENAME
import com.sourceclear.engine.component.collectors.npm.internal.NpmFileAnalyser.NPMFilePatterns.PACKAGE_FILENAME
import com.sourceclear.engine.component.collectors.npm.internal.NpmFileAnalyser.NPMFilePatterns.PACKAGE_LOCK_FILENAME
import com.sourceclear.engine.component.collectors.npm.internal.NpmFileAnalyser.NPMFilePatterns.NODE_MODULES_NAME
import org.junit.Test
import java.io.FileOutputStream
import java.nio.file.Files
import java.nio.file.Paths
import java.util.UUID
import java.util.zip.ZipEntry
import java.util.zip.ZipException
import java.util.zip.ZipOutputStream
import kotlin.test.assertEquals
import kotlin.test.assertFalse
import kotlin.test.assertTrue
import kotlin.test.fail

class ArchivesTest {
  @Test
  fun testUnzipJsInJar() {
    val file = Paths.get(ArchivesTest::class.java.classLoader.getResource("js-in-jar.jar")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      unZipJsInJar(file, it)
      assertTrue { Files.exists(it) }
      assertTrue { Files.exists(it.resolve(Paths.get("resources", PACKAGE_LOCK_FILENAME.value))) }
      assertTrue { Files.exists(it.resolve(Paths.get("resources", SHRINKWRAP_FILENAME.value))) }
      assertTrue { Files.exists(it.resolve(Paths.get("resources", PACKAGE_FILENAME.value))) }
      assertTrue { Files.exists(it.resolve(Paths.get("resources", NODE_MODULES_NAME.value, PACKAGE_FILENAME.value))) }
      assertTrue { Files.notExists(it.resolve(Paths.get("java"))) }
      assertTrue { Files.notExists(it.resolve(Paths.get("java", "there-is-no-java"))) }
    }
  }

  @Test
  fun testUnzipNoJsInJar() {
    val file = Paths.get(ArchivesTest::class.java.classLoader.getResource("foo.jar")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      val targetPath = it.resolve("foo")
      unZipJsInJar(file, targetPath)
      assertFalse { targetPath.toFile().exists() }
    }
  }

  @Test
  fun testUnzip() {
    val file = Paths.get(ArchivesTest::class.java.classLoader.getResource("foo.zip")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      unZip(file, it)
      assertTrue { Files.exists(it) }
      assertTrue { Files.exists(it.resolve(Paths.get("foo", "bar"))) }
      assertTrue { Files.exists(it.resolve(Paths.get("foo", "some-file"))) }
      assertTrue { Files.exists(it.resolve(Paths.get("foo", "bar", "some-file"))) }
    }
  }

  @Test
  fun testUnTarGz() {
    val stream = ArchivesTest::class.java.classLoader.getResourceAsStream("foo.tar.gz")!!
    CloseableTempIO.createTempDirectory("test").use {
      unTarGz(stream, it)
      assertTrue { Files.exists(it) }
      assertTrue { Files.exists(it.resolve(Paths.get("foo", "bar"))) }
      assertTrue { Files.exists(it.resolve(Paths.get("foo", "some-file"))) }
      assertTrue { Files.exists(it.resolve(Paths.get("foo", "bar", "some-file"))) }
    }
  }

  @Test
  fun testFileAlreadyExists() {
    val files = listOf(
        ArchivesTest::class.java.classLoader.getResource("foo.zip")!!,
        ArchivesTest::class.java.classLoader.getResource("foo.zip")!!
    ).map { Paths.get(it.toURI()) }

    CloseableTempIO.createTempFile("zip-of-zips", ".zip").use { zipFile ->
      ZipOutputStream(FileOutputStream(zipFile.toFile())).use { zipOutputStream ->
        for (f in files) {
          val zipEntry = ZipEntry(UUID.randomUUID().toString() + f.fileName)
          zipOutputStream.putNextEntry(zipEntry)
          Files.copy(f, zipOutputStream)
          zipOutputStream.closeEntry()
        }
      }

      CloseableTempIO.createTempDirectory("test").use {
        unZip(zipFile, it)
        assertEquals(2, Files.list(it).count())
        // Calling Files.list again because .count is a terminating operation

        val fooZips = Files.list(it)
        for (path in fooZips) {
          // Unzip the duplicated foo.zips in the same directory so that we can ensure that no
          // FileAlreadyExistsException will be thrown
          unZip(path, it)
        }

        assertTrue { Files.exists(it) }
        assertTrue { Files.exists(it.resolve(Paths.get("foo", "bar"))) }
        assertTrue { Files.exists(it.resolve(Paths.get("foo", "some-file"))) }
        assertTrue { Files.exists(it.resolve(Paths.get("foo", "bar", "some-file"))) }
      }
    }
  }

  @Test
  fun testImpossiblyLongFilenameInArchive() {
    val file = Paths.get(ArchivesTest::class.java.classLoader.getResource("long-name.zip")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      unZip(file, it)
      assertTrue(it.toFile().listFiles()!!.isEmpty())
    }
  }

  /**
    The example-mac-data-descriptor.zip file has an empty (0 bytes) file and the files have been zipped on MacOX with file descriptor
      ie > zip -fd -r OUTPUT_FILE INPUT_DIR
    Before this change, unzipping this kind of file would result in failure with something in the line of "Feature data descriptor not supported".
  **/
  @Test
  fun testUnzipMacDataDescriptor() {
    val file = Paths.get(ArchivesTest::class.java.classLoader.getResource("example-mac-data-descriptor.zip")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      unZip(file, it)
      assertTrue { Files.exists(it) }
      assertTrue { Files.exists(it.resolve(Paths.get("zipDirectory", "README.md"))) }
      assertTrue { Files.exists(it.resolve(Paths.get("zipDirectory", "emptyFile.txt"))) }
      assertTrue { Files.exists(it.resolve(Paths.get("zipDirectory", "textFile.txt"))) }
    }
  }

  /**
  The unsupportedCompressFilesInLZFSE.zip file is a compressed file using LZFSE compression. This was built on a Mac using
   the aa command tool:
    > aa archive -v -d <INPUT-DIR> -o <OUTPUT> -a lzfse
   Apache Commons Compress does not support LZFSE compression algorithm.
   **/
  @Test(expected = ZipException::class)
  fun testUnsupportedCompressFile() {
    val file = Paths.get(ArchivesTest::class.java.classLoader.getResource("unsupportedCompressFilesInLZFSE.zip")!!.toURI())
    CloseableTempIO.createTempDirectory("test").use {
      unZip(file, it)
      fail("Expected the provided unsupported file to fail unzipping")
    }
  }
}
