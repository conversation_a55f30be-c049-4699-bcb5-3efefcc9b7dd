package com.sourceclear.agent.wrapper.service.utils

import javax.crypto.Cipher
import javax.crypto.spec.IvParameterSpec
import javax.crypto.spec.SecretKeySpec


private val aesTransformation = "AES/CBC/PKCS5Padding"

private val aesAlgorithm = "AES"

fun aesCipherWith(key: ByteArray, iv: ByteArray, mode: Int = Cipher.DECRYPT_MODE): Cipher {
  val cipher = Cipher.getInstance(aesTransformation)
  val keySpec = SecretKeySpec(key, aesAlgorithm)
  val ivParameterSpec = IvParameterSpec(iv)
  cipher.init(mode, keySpec, ivParameterSpec)
  return cipher
}
